# frozen_string_literal: true

require 'prawn'
require 'prawn/table'

class PdfReportGenerator
  include ActionView::Helpers::NumberHelper
  
  attr_reader :report, :organization, :user, :options
  
  def initialize(report:, organization:, user:, options: {})
    @report = report
    @organization = organization
    @user = user
    @options = options
  end
  
  def generate_pdf_content
    pdf = Prawn::Document.new(
      page_size: 'A4',
      margin: [40, 40, 40, 40]
    )
    
    # Add header
    add_header(pdf)
    
    # Add report content based on type
    case report[:type]
    when 'daily_summary'
      generate_daily_summary_pdf(pdf)
    when 'weekly_report'
      generate_weekly_report_pdf(pdf)
    when 'monthly_analysis'
      generate_monthly_analysis_pdf(pdf)
    when 'sales_report'
      generate_sales_report_pdf(pdf)
    when 'inventory_report'
      generate_inventory_report_pdf(pdf)
    when 'financial_report'
      generate_financial_report_pdf(pdf)
    else
      generate_generic_report_pdf(pdf)
    end
    
    # Add footer
    add_footer(pdf)
    
    pdf.render
  end
  
  def generate
    # Generate PDF and save to temp file
    pdf_content = generate_pdf_content
    
    temp_file = Tempfile.new(['report', '.pdf'])
    temp_file.binmode
    temp_file.write(pdf_content)
    temp_file.rewind
    
    temp_file.path
  end
  
  private
  
  def add_header(pdf)
    pdf.bounding_box([0, pdf.cursor], width: pdf.bounds.width) do
      # Logo and company name
      if organization.logo.present?
        pdf.image organization.logo_path, height: 40, position: :left
      else
        pdf.text organization.name, size: 20, style: :bold
      end
      
      # Report title
      pdf.move_down 20
      pdf.text report[:type].humanize, size: 18, style: :bold
      pdf.text "Generated on #{Date.current.strftime('%B %d, %Y')}", size: 10, color: '666666'
      
      pdf.move_down 10
      pdf.stroke_horizontal_rule
      pdf.move_down 20
    end
  end
  
  def add_footer(pdf)
    pdf.repeat(:all) do
      pdf.bounding_box([0, 30], width: pdf.bounds.width) do
        pdf.stroke_horizontal_rule
        pdf.move_down 5
        pdf.text "Generated by DataFlow Pro • #{organization.name} • Page #{pdf.page_number}", 
                 size: 8, align: :center, color: '666666'
      end
    end
  end
  
  def generate_daily_summary_pdf(pdf)
    data = report[:data]
    
    # Summary section
    pdf.text "Executive Summary", size: 16, style: :bold
    pdf.move_down 10
    
    summary_data = [
      ["Metric", "Value", "Change"],
      ["Total Revenue", format_currency(data[:revenue][:total]), "#{data[:revenue][:change]}%"],
      ["Orders", data[:orders][:count].to_s, "#{data[:orders][:change]}%"],
      ["Average Order Value", format_currency(data[:orders][:average]), "#{data[:orders][:aov_change]}%"],
      ["New Customers", data[:customers][:new].to_s, "+#{data[:customers][:new]}"]
    ]
    
    pdf.table(summary_data, width: pdf.bounds.width) do |table|
      table.header = true
      table.row(0).font_style = :bold
      table.row(0).background_color = 'E0E0E0'
      table.column_widths = [200, 150, 100]
    end
    
    pdf.move_down 30
    
    # Top products section
    if data[:top_products].present?
      pdf.text "Top Products", size: 16, style: :bold
      pdf.move_down 10
      
      product_data = [["Rank", "Product", "Units Sold", "Revenue"]]
      data[:top_products].each_with_index do |product, i|
        product_data << [
          (i + 1).to_s,
          product[:name],
          product[:units].to_s,
          format_currency(product[:revenue])
        ]
      end
      
      pdf.table(product_data, width: pdf.bounds.width) do |table|
        table.header = true
        table.row(0).font_style = :bold
        table.row(0).background_color = 'E0E0E0'
      end
    end
    
    pdf.move_down 30
    
    # Key insights
    if data[:insights].present?
      pdf.text "Key Insights", size: 16, style: :bold
      pdf.move_down 10
      
      data[:insights].each do |insight|
        pdf.text "• #{insight}", size: 10
        pdf.move_down 5
      end
    end
  end
  
  def generate_weekly_report_pdf(pdf)
    data = report[:data]
    
    pdf.text "Weekly Performance Overview", size: 16, style: :bold
    pdf.text "Week of #{data[:week_start]} to #{data[:week_end]}", size: 12, color: '666666'
    pdf.move_down 20
    
    # Weekly summary
    summary_box(pdf, [
      { label: "Total Revenue", value: format_currency(data[:summary][:revenue]) },
      { label: "Total Orders", value: data[:summary][:orders] },
      { label: "Average Order Value", value: format_currency(data[:summary][:aov]) },
      { label: "Customer Retention", value: "#{data[:summary][:retention]}%" }
    ])
    
    pdf.move_down 30
    
    # Daily breakdown chart
    if data[:daily_breakdown].present?
      pdf.text "Daily Performance", size: 14, style: :bold
      pdf.move_down 10
      
      # Simple bar chart representation
      max_revenue = data[:daily_breakdown].values.map { |d| d[:revenue] }.max
      
      data[:daily_breakdown].each do |day, metrics|
        bar_width = (metrics[:revenue] / max_revenue.to_f) * 400
        
        pdf.text day, size: 10
        pdf.fill_color '4A90E2'
        pdf.fill_rectangle [60, pdf.cursor], bar_width, 15
        pdf.fill_color '000000'
        pdf.draw_text format_currency(metrics[:revenue]), at: [bar_width + 70, pdf.cursor - 10]
        pdf.move_down 20
      end
    end
  end
  
  def generate_monthly_analysis_pdf(pdf)
    data = report[:data]
    
    pdf.text "Monthly Business Analysis", size: 18, style: :bold
    pdf.text data[:month], size: 14, color: '666666'
    pdf.move_down 20
    
    # Executive summary
    pdf.text "Executive Summary", size: 14, style: :bold
    pdf.move_down 10
    pdf.text data[:executive_summary], size: 10
    pdf.move_down 20
    
    # Performance metrics
    if data[:performance].present?
      pdf.text "Performance Metrics", size: 14, style: :bold
      pdf.move_down 10
      
      metrics_data = []
      data[:performance].each do |metric, value|
        metrics_data << [metric.to_s.humanize, format_metric_value(value)]
      end
      
      pdf.table(metrics_data, width: pdf.bounds.width) do |table|
        table.column_widths = [300, 150]
        table.cells.border_width = 0.5
      end
    end
    
    pdf.move_down 30
    
    # Recommendations
    if data[:recommendations].present?
      pdf.text "Strategic Recommendations", size: 14, style: :bold
      pdf.move_down 10
      
      data[:recommendations].each_with_index do |recommendation, i|
        pdf.text "#{i + 1}. #{recommendation}", size: 10
        pdf.move_down 8
      end
    end
  end
  
  def generate_sales_report_pdf(pdf)
    data = report[:data]
    
    pdf.text "Sales Performance Report", size: 18, style: :bold
    pdf.text "Period: #{data[:period]}", size: 12, color: '666666'
    pdf.move_down 20
    
    # Sales summary
    summary_data = [
      ["Total Sales", format_currency(data[:total_sales])],
      ["Number of Transactions", number_with_delimiter(data[:transaction_count])],
      ["Average Sale Value", format_currency(data[:average_sale])],
      ["Growth vs Last Period", "#{data[:growth]}%"],
      ["Top Sales Channel", data[:top_channel]]
    ]
    
    pdf.table(summary_data, width: pdf.bounds.width) do |table|
      table.column_widths = [300, 150]
      table.cells.border_width = 0.5
      table.column(0).font_style = :bold
    end
    
    pdf.move_down 30
    
    # Sales by category
    if data[:sales_by_category].present?
      pdf.text "Sales by Category", size: 14, style: :bold
      pdf.move_down 10
      
      category_data = [["Category", "Sales", "Units", "% of Total"]]
      data[:sales_by_category].each do |category|
        category_data << [
          category[:name],
          format_currency(category[:sales]),
          number_with_delimiter(category[:units]),
          "#{category[:percentage]}%"
        ]
      end
      
      pdf.table(category_data, width: pdf.bounds.width) do |table|
        table.header = true
        table.row(0).font_style = :bold
        table.row(0).background_color = 'E0E0E0'
      end
    end
  end
  
  def generate_inventory_report_pdf(pdf)
    data = report[:data]
    
    pdf.text "Inventory Status Report", size: 18, style: :bold
    pdf.text "As of #{Date.current.strftime('%B %d, %Y')}", size: 12, color: '666666'
    pdf.move_down 20
    
    # Inventory summary
    pdf.text "Summary", size: 14, style: :bold
    pdf.move_down 10
    
    summary_box(pdf, [
      { label: "Total SKUs", value: data[:total_skus] },
      { label: "Total Value", value: format_currency(data[:total_value]) },
      { label: "Low Stock Items", value: data[:low_stock_count] },
      { label: "Out of Stock", value: data[:out_of_stock_count] }
    ])
    
    pdf.move_down 30
    
    # Low stock alerts
    if data[:low_stock_items].present?
      pdf.text "Low Stock Alerts", size: 14, style: :bold, color: 'FF0000'
      pdf.move_down 10
      
      low_stock_data = [["Item", "Current Stock", "Reorder Point", "Days Until Stockout"]]
      data[:low_stock_items].each do |item|
        low_stock_data << [
          item[:name],
          item[:current_stock].to_s,
          item[:reorder_point].to_s,
          item[:days_until_stockout].to_s
        ]
      end
      
      pdf.table(low_stock_data, width: pdf.bounds.width) do |table|
        table.header = true
        table.row(0).font_style = :bold
        table.row(0).background_color = 'FFE0E0'
      end
    end
  end
  
  def generate_financial_report_pdf(pdf)
    data = report[:data]
    
    pdf.text "Financial Report", size: 18, style: :bold
    pdf.text "Period: #{data[:period]}", size: 12, color: '666666'
    pdf.move_down 20
    
    # Income statement
    pdf.text "Income Statement", size: 14, style: :bold
    pdf.move_down 10
    
    income_data = [
      ["", "Amount", "% of Revenue"],
      ["Revenue", format_currency(data[:revenue]), "100.0%"],
      ["Cost of Goods Sold", format_currency(data[:cogs]), "#{data[:cogs_percentage]}%"],
      ["Gross Profit", format_currency(data[:gross_profit]), "#{data[:gross_margin]}%"],
      ["Operating Expenses", format_currency(data[:operating_expenses]), "#{data[:opex_percentage]}%"],
      ["Net Income", format_currency(data[:net_income]), "#{data[:net_margin]}%"]
    ]
    
    pdf.table(income_data, width: pdf.bounds.width) do |table|
      table.header = true
      table.row(0).font_style = :bold
      table.row(0).background_color = 'E0E0E0'
      table.row(3).font_style = :bold
      table.row(5).font_style = :bold
      table.row(5).background_color = 'E8F5E9'
    end
    
    pdf.move_down 30
    
    # Key financial ratios
    pdf.text "Key Financial Ratios", size: 14, style: :bold
    pdf.move_down 10
    
    ratios_data = []
    data[:financial_ratios].each do |ratio, value|
      ratios_data << [ratio.to_s.humanize, format_ratio_value(value)]
    end
    
    pdf.table(ratios_data, width: pdf.bounds.width) do |table|
      table.column_widths = [300, 150]
      table.cells.border_width = 0.5
    end
  end
  
  def generate_generic_report_pdf(pdf)
    data = report[:data]
    
    pdf.text report[:type].humanize, size: 18, style: :bold
    pdf.move_down 20
    
    # Iterate through data sections
    data.each do |section, content|
      pdf.text section.to_s.humanize, size: 14, style: :bold
      pdf.move_down 10
      
      case content
      when Hash
        table_data = content.map { |k, v| [k.to_s.humanize, format_value(v)] }
        pdf.table(table_data, width: pdf.bounds.width) do |table|
          table.column_widths = [300, 150]
          table.cells.border_width = 0.5
        end
      when Array
        content.each_with_index do |item, i|
          pdf.text "#{i + 1}. #{format_value(item)}", size: 10
          pdf.move_down 5
        end
      else
        pdf.text format_value(content), size: 10
      end
      
      pdf.move_down 20
    end
  end
  
  def summary_box(pdf, items)
    pdf.bounding_box([0, pdf.cursor], width: pdf.bounds.width) do
      items.each_with_index do |item, i|
        x_position = (i % 2) * (pdf.bounds.width / 2)
        y_position = pdf.cursor - ((i / 2) * 60)
        
        pdf.bounding_box([x_position, y_position], width: pdf.bounds.width / 2 - 10, height: 50) do
          pdf.stroke_bounds
          pdf.move_down 10
          pdf.text item[:label], size: 10, align: :center
          pdf.text item[:value].to_s, size: 14, style: :bold, align: :center
        end
      end
      
      pdf.move_down (((items.length + 1) / 2) * 60)
    end
  end
  
  def format_currency(amount)
    return "N/A" if amount.nil?
    number_to_currency(amount)
  end
  
  def format_metric_value(value)
    case value
    when Numeric
      number_with_delimiter(value.round(2))
    when Hash
      value[:formatted] || format_value(value[:value])
    else
      value.to_s
    end
  end
  
  def format_ratio_value(value)
    case value
    when Numeric
      "#{(value * 100).round(2)}%"
    when Hash
      value[:formatted] || "#{value[:value]}%"
    else
      value.to_s
    end
  end
  
  def format_value(value)
    case value
    when Numeric
      number_with_delimiter(value)
    when Date, Time
      value.strftime('%B %d, %Y')
    when TrueClass
      "Yes"
    when FalseClass
      "No"
    when NilClass
      "N/A"
    else
      value.to_s
    end
  end
end