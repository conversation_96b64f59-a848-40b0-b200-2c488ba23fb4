# frozen_string_literal: true

module DeliveryChannels
  # Email delivery channel with HTML templates and PDF attachments
  class EmailChannel < BaseChannel
    def deliver
      return handle_delivery_error(StandardError.new('Channel not configured')) unless configured?
      return handle_delivery_error(StandardError.new('Invalid delivery parameters')) unless valid_delivery?
      
      begin
        mailer_response = case report[:format]
        when 'pdf'
          deliver_with_pdf_attachment
        when 'html'
          deliver_html_email
        else
          deliver_text_email
        end
        
        if mailer_response
          log_delivery(
            status: 'delivered',
            metadata: {
              email: recipient_email,
              subject: email_subject,
              format: report[:format],
              attachments: report[:format] == 'pdf' ? 1 : 0
            }
          )
          { success: true, message_id: mailer_response.message_id }
        else
          handle_delivery_error(StandardError.new('Email delivery failed'))
        end
      rescue => e
        handle_delivery_error(e)
      end
    end
    
    def configured?
      ActionMailer::Base.delivery_method != :test &&
        recipient_email.present?
    end
    
    def format_content
      {
        subject: email_subject,
        body: email_body,
        attachments: generate_attachments
      }
    end
    
    private
    
    def recipient_email
      user.email
    end
    
    def email_subject
      custom_subject = options[:subject]
      return custom_subject if custom_subject.present?
      
      "#{organization.name} - #{report[:type].humanize} - #{Date.current}"
    end
    
    def email_body
      case report[:format]
      when 'html'
        render_html_template
      else
        render_text_content
      end
    end
    
    def deliver_with_pdf_attachment
      pdf_content = generate_pdf_report
      
      ReportMailer.with(
        user: user,
        organization: organization,
        subject: email_subject,
        body: render_text_content,
        pdf_content: pdf_content,
        pdf_filename: pdf_filename
      ).report_with_attachment.deliver_now
    end
    
    def deliver_html_email
      ReportMailer.with(
        user: user,
        organization: organization,
        subject: email_subject,
        html_body: render_html_template,
        text_body: render_text_content
      ).html_report.deliver_now
    end
    
    def deliver_text_email
      ReportMailer.with(
        user: user,
        organization: organization,
        subject: email_subject,
        body: render_text_content
      ).text_report.deliver_now
    end
    
    def render_html_template
      ApplicationController.render(
        template: "report_mailer/#{report[:type]}",
        layout: 'mailer',
        assigns: {
          user: user,
          organization: organization,
          report_data: report[:data],
          report_type: report[:type],
          generated_at: Time.current
        }
      )
    rescue ActionView::MissingTemplate
      # Fallback to generic template
      ApplicationController.render(
        template: 'report_mailer/generic_report',
        layout: 'mailer',
        assigns: {
          user: user,
          organization: organization,
          report_data: report[:data],
          report_type: report[:type],
          generated_at: Time.current
        }
      )
    end
    
    def render_text_content
      data = report[:data]
      
      content = "#{report[:type].humanize} Report\n"
      content += "="*50 + "\n\n"
      content += "Organization: #{organization.name}\n"
      content += "Generated: #{Time.current.strftime('%B %d, %Y at %I:%M %p')}\n\n"
      
      case report[:type]
      when 'daily_summary'
        content += format_daily_summary(data)
      when 'weekly_report'
        content += format_weekly_report(data)
      when 'monthly_analysis'
        content += format_monthly_analysis(data)
      when 'sales_report'
        content += format_sales_report(data)
      else
        content += format_generic_report(data)
      end
      
      content += "\n\n"
      content += "-"*50 + "\n"
      content += "This report was automatically generated by DataFlow Pro.\n"
      content += "To update your delivery preferences, visit: #{preferences_url}\n"
      
      content
    end
    
    def format_daily_summary(data)
      summary = "DAILY BUSINESS SUMMARY\n\n"
      
      if data[:revenue]
        summary += "Revenue\n"
        summary += "- Total: $#{format_number(data[:revenue][:total])}\n"
        summary += "- Change: #{data[:revenue][:change]}%\n"
        summary += "- Transactions: #{data[:revenue][:count]}\n\n"
      end
      
      if data[:top_products]
        summary += "Top Products\n"
        data[:top_products].each_with_index do |product, i|
          summary += "#{i + 1}. #{product[:name]} - #{product[:units]} units ($#{format_number(product[:revenue])})\n"
        end
        summary += "\n"
      end
      
      if data[:key_metrics]
        summary += "Key Metrics\n"
        data[:key_metrics].each do |metric, value|
          summary += "- #{metric.to_s.humanize}: #{format_metric_value(value)}\n"
        end
      end
      
      summary
    end
    
    def format_weekly_report(data)
      report = "WEEKLY PERFORMANCE REPORT\n\n"
      
      report += "Week: #{data[:week_start]} to #{data[:week_end]}\n\n"
      
      if data[:summary]
        report += "Summary\n"
        report += "- Total Revenue: $#{format_number(data[:summary][:revenue])}\n"
        report += "- Total Orders: #{data[:summary][:orders]}\n"
        report += "- Average Order Value: $#{format_number(data[:summary][:aov])}\n\n"
      end
      
      if data[:daily_breakdown]
        report += "Daily Breakdown\n"
        data[:daily_breakdown].each do |day, metrics|
          report += "#{day}: $#{format_number(metrics[:revenue])} (#{metrics[:orders]} orders)\n"
        end
      end
      
      report
    end
    
    def format_monthly_analysis(data)
      analysis = "MONTHLY BUSINESS ANALYSIS\n\n"
      
      analysis += "Month: #{data[:month]}\n\n"
      
      if data[:performance]
        analysis += "Performance Overview\n"
        analysis += "- Revenue: $#{format_number(data[:performance][:revenue])}\n"
        analysis += "- Growth: #{data[:performance][:growth]}%\n"
        analysis += "- Profit Margin: #{data[:performance][:margin]}%\n\n"
      end
      
      if data[:insights]
        analysis += "Key Insights\n"
        data[:insights].each do |insight|
          analysis += "• #{insight}\n"
        end
        analysis += "\n"
      end
      
      if data[:recommendations]
        analysis += "Recommendations\n"
        data[:recommendations].each do |recommendation|
          analysis += "• #{recommendation}\n"
        end
      end
      
      analysis
    end
    
    def format_sales_report(data)
      "SALES REPORT\n\n" +
      "Period: #{data[:period]}\n\n" +
      "Total Sales: $#{format_number(data[:total_sales])}\n" +
      "Number of Transactions: #{data[:transaction_count]}\n" +
      "Average Sale: $#{format_number(data[:average_sale])}\n" +
      "Top Customer: #{data[:top_customer]}\n" +
      "Best Selling Product: #{data[:best_product]}\n"
    end
    
    def format_generic_report(data)
      report = ""
      
      data.each do |section, content|
        report += "#{section.to_s.humanize.upcase}\n"
        
        if content.is_a?(Hash)
          content.each do |key, value|
            report += "- #{key.to_s.humanize}: #{format_value(value)}\n"
          end
        elsif content.is_a?(Array)
          content.each_with_index do |item, i|
            report += "#{i + 1}. #{format_value(item)}\n"
          end
        else
          report += "#{format_value(content)}\n"
        end
        
        report += "\n"
      end
      
      report
    end
    
    def format_number(number)
      number.to_s.gsub(/(\d)(?=(\d\d\d)+(?!\d))/, '\\1,')
    end
    
    def format_metric_value(value)
      case value
      when Numeric
        value % 1 == 0 ? value.to_i.to_s : "%.2f" % value
      when Hash
        value[:formatted] || value[:value] || value.to_s
      else
        value.to_s
      end
    end
    
    def format_value(value)
      case value
      when Numeric
        format_number(value)
      when Date, Time
        value.strftime('%B %d, %Y')
      when Hash
        value.map { |k, v| "#{k}: #{v}" }.join(', ')
      else
        value.to_s
      end
    end
    
    def generate_pdf_report
      PdfReportGenerator.new(
        report: report,
        organization: organization,
        user: user
      ).generate_pdf_content
    end
    
    def pdf_filename
      "#{organization.name.parameterize}_#{report[:type]}_#{Date.current}.pdf"
    end
    
    def generate_attachments
      return [] unless report[:format] == 'pdf'
      
      [{
        filename: pdf_filename,
        content: generate_pdf_report,
        mime_type: 'application/pdf'
      }]
    end
    
    def preferences_url
      Rails.application.routes.url_helpers.delivery_preferences_url(
        host: ENV['APP_HOST'] || 'localhost:3000'
      )
    end
  end
end