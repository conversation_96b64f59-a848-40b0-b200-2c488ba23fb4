# frozen_string_literal: true

require 'powerpoint'

module DeliveryChannels
  # PowerPoint/Google Slides generation channel
  class SlidesChannel < BaseChannel
    def deliver
      return handle_delivery_error(StandardError.new('Channel not configured')) unless configured?
      return handle_delivery_error(StandardError.new('Invalid delivery parameters')) unless valid_delivery?
      
      begin
        # Generate slides
        slides_path = generate_slides
        
        # Upload to cloud storage or send via email
        delivery_method = options[:delivery_method] || 'email'
        
        case delivery_method
        when 'email'
          deliver_via_email(slides_path)
        when 'download'
          provide_download_link(slides_path)
        when 'google_drive'
          upload_to_google_drive(slides_path)
        else
          handle_delivery_error(StandardError.new("Unknown delivery method: #{delivery_method}"))
        end
        
        log_delivery(
          status: 'delivered',
          metadata: {
            slides_count: count_slides,
            file_size: File.size(slides_path),
            delivery_method: delivery_method,
            filename: slides_filename
          }
        )
        
        { success: true, file_path: slides_path }
      rescue => e
        handle_delivery_error(e)
      end
    end
    
    def configured?
      # Slides generation doesn't require external configuration
      true
    end
    
    def format_content
      # This method generates the actual PowerPoint file
      generate_slides
    end
    
    private
    
    def generate_slides
      presentation = Powerpoint::Presentation.new
      
      # Add title slide
      add_title_slide(presentation)
      
      # Add content slides based on report type
      case report[:type]
      when 'daily_summary'
        add_daily_summary_slides(presentation)
      when 'weekly_report'
        add_weekly_report_slides(presentation)
      when 'monthly_analysis'
        add_monthly_analysis_slides(presentation)
      when 'sales_report'
        add_sales_report_slides(presentation)
      when 'financial_report'
        add_financial_report_slides(presentation)
      else
        add_generic_report_slides(presentation)
      end
      
      # Add closing slide
      add_closing_slide(presentation)
      
      # Save to temp file
      temp_file = Tempfile.new([slides_filename_base, '.pptx'])
      presentation.save(temp_file.path)
      
      temp_file.path
    end
    
    def add_title_slide(presentation)
      title_slide = presentation.add_slide('Title Slide')
      
      title_slide.add_title(report[:type].humanize)
      title_slide.add_subtitle([
        organization.name,
        Date.current.strftime('%B %d, %Y'),
        "Generated by DataFlow Pro"
      ].join("\n"))
    end
    
    def add_daily_summary_slides(presentation)
      data = report[:data]
      
      # Executive summary slide
      summary_slide = presentation.add_slide('Title and Content')
      summary_slide.add_title('Executive Summary')
      
      bullet_points = []
      bullet_points << "Total Revenue: #{format_currency(data[:revenue][:total])} (#{format_change(data[:revenue][:change])})"
      bullet_points << "Orders: #{data[:orders][:count]} (#{format_change(data[:orders][:change])})"
      bullet_points << "Average Order Value: #{format_currency(data[:orders][:average])}"
      bullet_points << "New Customers: #{data[:customers][:new]}"
      
      summary_slide.add_content(bullet_points)
      
      # Top products slide
      if data[:top_products].present?
        products_slide = presentation.add_slide('Title and Content')
        products_slide.add_title('Top Products')
        
        product_list = data[:top_products].first(5).map do |product|
          "#{product[:name]}: #{product[:units]} units (#{format_currency(product[:revenue])})"
        end
        
        products_slide.add_content(product_list)
      end
      
      # Key metrics slide with chart
      if data[:metrics_chart].present?
        chart_slide = presentation.add_slide('Title and Chart')
        chart_slide.add_title('Performance Metrics')
        chart_slide.add_chart(
          type: :column,
          data: data[:metrics_chart],
          categories: data[:metrics_chart][:categories],
          series: data[:metrics_chart][:series]
        )
      end
    end
    
    def add_weekly_report_slides(presentation)
      data = report[:data]
      
      # Week overview slide
      overview_slide = presentation.add_slide('Title and Content')
      overview_slide.add_title("Week of #{data[:week_start]} - #{data[:week_end]}")
      
      overview_points = [
        "Total Revenue: #{format_currency(data[:summary][:revenue])}",
        "Total Orders: #{data[:summary][:orders]}",
        "Average Order Value: #{format_currency(data[:summary][:aov])}",
        "Week-over-Week Growth: #{data[:summary][:growth]}%"
      ]
      
      overview_slide.add_content(overview_points)
      
      # Daily breakdown chart
      if data[:daily_breakdown].present?
        daily_slide = presentation.add_slide('Title and Chart')
        daily_slide.add_title('Daily Performance')
        
        chart_data = {
          categories: data[:daily_breakdown].keys,
          series: [{
            name: 'Revenue',
            data: data[:daily_breakdown].values.map { |d| d[:revenue] }
          }]
        }
        
        daily_slide.add_chart(type: :line, data: chart_data)
      end
      
      # Insights slide
      if data[:insights].present?
        insights_slide = presentation.add_slide('Title and Content')
        insights_slide.add_title('Key Insights')
        insights_slide.add_content(data[:insights])
      end
    end
    
    def add_monthly_analysis_slides(presentation)
      data = report[:data]
      
      # Executive summary slide
      exec_slide = presentation.add_slide('Title and Content')
      exec_slide.add_title('Monthly Executive Summary')
      exec_slide.add_content([data[:executive_summary]])
      
      # Performance metrics slide
      if data[:performance].present?
        metrics_slide = presentation.add_slide('Two Content')
        metrics_slide.add_title('Performance Metrics')
        
        left_metrics = []
        right_metrics = []
        
        data[:performance].each_with_index do |(metric, value), index|
          formatted = "#{metric.to_s.humanize}: #{format_metric_value(value)}"
          if index.even?
            left_metrics << formatted
          else
            right_metrics << formatted
          end
        end
        
        metrics_slide.add_content(left_metrics, position: :left)
        metrics_slide.add_content(right_metrics, position: :right)
      end
      
      # Trend analysis slide with chart
      if data[:trend_data].present?
        trend_slide = presentation.add_slide('Title and Chart')
        trend_slide.add_title('Monthly Trends')
        trend_slide.add_chart(
          type: :line,
          data: data[:trend_data]
        )
      end
      
      # Recommendations slide
      if data[:recommendations].present?
        rec_slide = presentation.add_slide('Title and Content')
        rec_slide.add_title('Strategic Recommendations')
        rec_slide.add_content(data[:recommendations])
      end
    end
    
    def add_sales_report_slides(presentation)
      data = report[:data]
      
      # Sales overview slide
      overview_slide = presentation.add_slide('Title and Content')
      overview_slide.add_title('Sales Overview')
      
      overview_content = [
        "Period: #{data[:period]}",
        "Total Sales: #{format_currency(data[:total_sales])}",
        "Transactions: #{number_with_delimiter(data[:transaction_count])}",
        "Average Sale: #{format_currency(data[:average_sale])}",
        "Growth: #{data[:growth]}%"
      ]
      
      overview_slide.add_content(overview_content)
      
      # Sales by category slide
      if data[:sales_by_category].present?
        category_slide = presentation.add_slide('Title and Table')
        category_slide.add_title('Sales by Category')
        
        table_data = [['Category', 'Sales', 'Units', '% of Total']]
        data[:sales_by_category].each do |category|
          table_data << [
            category[:name],
            format_currency(category[:sales]),
            number_with_delimiter(category[:units]),
            "#{category[:percentage]}%"
          ]
        end
        
        category_slide.add_table(table_data)
      end
      
      # Top customers slide
      if data[:top_customers].present?
        customers_slide = presentation.add_slide('Title and Content')
        customers_slide.add_title('Top Customers')
        
        customer_list = data[:top_customers].first(10).map do |customer|
          "#{customer[:name]}: #{format_currency(customer[:total_spent])}"
        end
        
        customers_slide.add_content(customer_list)
      end
    end
    
    def add_financial_report_slides(presentation)
      data = report[:data]
      
      # Income statement slide
      income_slide = presentation.add_slide('Title and Table')
      income_slide.add_title('Income Statement')
      
      income_table = [
        ['', 'Amount', '% of Revenue'],
        ['Revenue', format_currency(data[:revenue]), '100.0%'],
        ['COGS', format_currency(data[:cogs]), "#{data[:cogs_percentage]}%"],
        ['Gross Profit', format_currency(data[:gross_profit]), "#{data[:gross_margin]}%"],
        ['Operating Expenses', format_currency(data[:operating_expenses]), "#{data[:opex_percentage]}%"],
        ['Net Income', format_currency(data[:net_income]), "#{data[:net_margin]}%"]
      ]
      
      income_slide.add_table(income_table)
      
      # Financial ratios slide
      if data[:financial_ratios].present?
        ratios_slide = presentation.add_slide('Title and Content')
        ratios_slide.add_title('Key Financial Ratios')
        
        ratio_list = data[:financial_ratios].map do |ratio, value|
          "#{ratio.to_s.humanize}: #{format_ratio_value(value)}"
        end
        
        ratios_slide.add_content(ratio_list)
      end
      
      # Cash flow chart
      if data[:cash_flow_data].present?
        cash_slide = presentation.add_slide('Title and Chart')
        cash_slide.add_title('Cash Flow Trend')
        cash_slide.add_chart(
          type: :area,
          data: data[:cash_flow_data]
        )
      end
    end
    
    def add_generic_report_slides(presentation)
      data = report[:data]
      
      # Create slides for each major section
      data.each do |section, content|
        next if content.blank?
        
        case content
        when Hash
          add_hash_slide(presentation, section, content)
        when Array
          add_list_slide(presentation, section, content)
        else
          add_text_slide(presentation, section, content)
        end
      end
    end
    
    def add_hash_slide(presentation, title, content)
      slide = presentation.add_slide('Title and Content')
      slide.add_title(title.to_s.humanize)
      
      bullet_points = content.map do |key, value|
        "#{key.to_s.humanize}: #{format_value(value)}"
      end
      
      slide.add_content(bullet_points)
    end
    
    def add_list_slide(presentation, title, content)
      slide = presentation.add_slide('Title and Content')
      slide.add_title(title.to_s.humanize)
      
      # Limit to 10 items per slide
      content.first(10).each_with_index do |item, index|
        slide.add_content("#{index + 1}. #{format_value(item)}")
      end
      
      if content.length > 10
        slide.add_content("... and #{content.length - 10} more items")
      end
    end
    
    def add_text_slide(presentation, title, content)
      slide = presentation.add_slide('Title and Content')
      slide.add_title(title.to_s.humanize)
      slide.add_content([format_value(content)])
    end
    
    def add_closing_slide(presentation)
      closing_slide = presentation.add_slide('Title Slide')
      closing_slide.add_title('Thank You')
      closing_slide.add_subtitle([
        "Questions?",
        "",
        "Generated by DataFlow Pro",
        "#{Date.current.strftime('%B %d, %Y')}",
        "",
        organization.name
      ].join("\n"))
    end
    
    def deliver_via_email(slides_path)
      ReportMailer.with(
        user: user,
        organization: organization,
        subject: "#{report[:type].humanize} Presentation - #{Date.current}",
        body: "Please find attached your automated presentation.",
        attachment_path: slides_path,
        attachment_name: slides_filename
      ).presentation_delivery.deliver_now
    end
    
    def provide_download_link(slides_path)
      # Upload to cloud storage and generate temporary download link
      url = CloudStorageService.new.upload_temporary(
        slides_path,
        filename: slides_filename,
        expires_in: 24.hours
      )
      
      # Send notification with download link
      NotificationService.new.notify(
        user: user,
        title: "Your presentation is ready",
        message: "Download your #{report[:type].humanize} presentation",
        action_url: url
      )
      
      url
    end
    
    def upload_to_google_drive(slides_path)
      # This would integrate with Google Drive API
      GoogleDriveService.new(user: user).upload_presentation(
        file_path: slides_path,
        filename: slides_filename,
        folder: options[:google_drive_folder]
      )
    end
    
    def count_slides
      # This is a placeholder - actual implementation would count slides in the file
      case report[:type]
      when 'daily_summary' then 4
      when 'weekly_report' then 5
      when 'monthly_analysis' then 6
      when 'financial_report' then 5
      else 3
      end
    end
    
    def slides_filename_base
      "#{organization.name.parameterize}_#{report[:type]}_#{Date.current}"
    end
    
    def slides_filename
      "#{slides_filename_base}.pptx"
    end
    
    def format_currency(amount)
      return "N/A" if amount.nil?
      "$#{number_with_delimiter(amount.round(2))}"
    end
    
    def format_change(change)
      return "0%" if change.nil? || change.zero?
      
      sign = change > 0 ? "+" : ""
      "#{sign}#{change}%"
    end
    
    def format_metric_value(value)
      case value
      when Numeric
        number_with_delimiter(value.round(2))
      when Hash
        value[:formatted] || format_value(value[:value])
      else
        value.to_s
      end
    end
    
    def format_ratio_value(value)
      case value
      when Numeric
        "#{(value * 100).round(2)}%"
      when Hash
        value[:formatted] || "#{value[:value]}%"
      else
        value.to_s
      end
    end
    
    def format_value(value)
      case value
      when Numeric
        number_with_delimiter(value)
      when Date, Time
        value.strftime('%B %d, %Y')
      when TrueClass
        "Yes"
      when FalseClass
        "No"
      when NilClass
        "N/A"
      else
        value.to_s
      end
    end
    
    def number_with_delimiter(number)
      number.to_s.gsub(/(\d)(?=(\d\d\d)+(?!\d))/, '\\1,')
    end
  end
end