<div id="<%= dom_id(delivery_preference) %>" class="px-6 py-4 hover:bg-gray-50 transition-colors duration-150">
  <div class="flex items-center justify-between">
    <div class="flex items-center space-x-4">
      <!-- Channel Icon -->
      <div class="flex-shrink-0">
        <% case delivery_preference.channel %>
        <% when 'email' %>
          <span class="text-3xl">📧</span>
        <% when 'whatsapp' %>
          <span class="text-3xl">💬</span>
        <% when 'sms' %>
          <span class="text-3xl">📱</span>
        <% when 'pdf' %>
          <span class="text-3xl">📄</span>
        <% when 'slides' %>
          <span class="text-3xl">📊</span>
        <% end %>
      </div>
      
      <!-- Details -->
      <div class="flex-1">
        <div class="flex items-center space-x-2">
          <h4 class="text-sm font-medium text-gray-900">
            <%= delivery_preference.channel.capitalize %>
          </h4>
          <span class="text-xs text-gray-500">
            (<%= delivery_preference.format %>)
          </span>
          
          <% if delivery_preference.scheduled? %>
            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
              <svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
              </svg>
              <%= delivery_preference.schedule_description %>
            </span>
          <% else %>
            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
              On-demand
            </span>
          <% end %>
        </div>
        
        <% if delivery_preference.scheduled? && delivery_preference.delivery_time.present? %>
          <p class="mt-1 text-xs text-gray-500">
            Delivers at <%= delivery_preference.delivery_time %> 
            <%= delivery_preference.timezone || current_user.timezone || 'UTC' %>
          </p>
        <% end %>
      </div>
    </div>
    
    <!-- Actions -->
    <div class="flex items-center space-x-2">
      <!-- Active/Inactive Toggle -->
      <%= button_to toggle_delivery_preference_path(delivery_preference), 
          method: :patch,
          form: { data: { turbo_frame: "_top" } },
          class: "relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 #{delivery_preference.active? ? 'bg-indigo-600' : 'bg-gray-200'}" do %>
        <span class="sr-only">Toggle preference</span>
        <span aria-hidden="true" class="<%= delivery_preference.active? ? 'translate-x-5' : 'translate-x-0' %> pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
      <% end %>
      
      <!-- Preview -->
      <%= link_to preview_delivery_preference_path(delivery_preference), 
          data: { turbo_frame: "preview_modal" },
          class: "text-gray-400 hover:text-gray-500" do %>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      <% end %>
      
      <!-- Test Delivery -->
      <%= link_to test_delivery_delivery_preference_path(delivery_preference), 
          data: { turbo_method: :post },
          class: "text-gray-400 hover:text-gray-500" do %>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
        </svg>
      <% end %>
      
      <!-- Edit -->
      <%= link_to edit_delivery_preference_path(delivery_preference), 
          class: "text-gray-400 hover:text-gray-500" do %>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      <% end %>
      
      <!-- Delete -->
      <%= link_to delivery_preference_path(delivery_preference), 
          data: { 
            turbo_method: :delete,
            turbo_confirm: "Are you sure you want to delete this preference?"
          },
          class: "text-gray-400 hover:text-red-500" do %>
        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      <% end %>
    </div>
  </div>
</div>