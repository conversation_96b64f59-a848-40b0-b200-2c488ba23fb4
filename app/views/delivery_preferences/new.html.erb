<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
      <h1 class="text-xl font-semibold text-gray-900">Create Delivery Preference</h1>
    </div>
    
    <%= form_with model: @delivery_preference, local: true, class: "space-y-6 p-6" do |f| %>
      <% if @delivery_preference.errors.any? %>
        <div class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                There were <%= pluralize(@delivery_preference.errors.count, "error") %> with your submission
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <% @delivery_preference.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Report Type -->
      <div>
        <%= f.label :report_type, class: "block text-sm font-medium text-gray-700" %>
        <%= f.select :report_type, 
            options_for_select(DeliveryPreference::REPORT_TYPES.map { |rt| [rt.humanize, rt] }, @delivery_preference.report_type),
            { prompt: "Select a report type" },
            class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
        <p class="mt-2 text-sm text-gray-500">Choose which type of report you want to receive</p>
      </div>

      <!-- Channel -->
      <div>
        <%= f.label :channel, "Delivery Channel", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-2 grid grid-cols-2 gap-3 sm:grid-cols-3">
          <% DeliveryPreference::CHANNELS.each do |channel| %>
            <label class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none channel-option">
              <%= f.radio_button :channel, channel, class: "sr-only", data: { channel: channel } %>
              <div class="flex flex-1">
                <div class="flex flex-col">
                  <span class="block text-2xl mb-1">
                    <% case channel %>
                    <% when 'email' %>📧
                    <% when 'whatsapp' %>💬
                    <% when 'sms' %>📱
                    <% when 'pdf' %>📄
                    <% when 'slides' %>📊
                    <% end %>
                  </span>
                  <span class="block text-sm font-medium text-gray-900">
                    <%= channel.capitalize %>
                  </span>
                </div>
              </div>
              <div class="absolute -inset-px rounded-lg border-2 pointer-events-none" aria-hidden="true"></div>
            </label>
          <% end %>
        </div>
      </div>

      <!-- Format -->
      <div id="format_field">
        <%= f.label :format, "Report Format", class: "block text-sm font-medium text-gray-700" %>
        <%= f.select :format, 
            options_for_select(DeliveryPreference::FORMATS.map { |format| [format.capitalize, format] }, @delivery_preference.format),
            {},
            class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
        <p class="mt-2 text-sm text-gray-500">Choose how the report should be formatted</p>
      </div>

      <!-- Schedule -->
      <div>
        <%= f.label :schedule, "Delivery Schedule", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-2 space-y-4">
          <label class="relative flex items-start">
            <%= f.radio_button :schedule, '', class: "h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500" %>
            <div class="ml-3 text-sm">
              <span class="font-medium text-gray-700">On-demand only</span>
              <p class="text-gray-500">Receive reports only when manually requested</p>
            </div>
          </label>
          
          <label class="relative flex items-start">
            <%= f.radio_button :schedule, 'daily', class: "h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500" %>
            <div class="ml-3 text-sm">
              <span class="font-medium text-gray-700">Daily</span>
              <p class="text-gray-500">Receive reports every day</p>
            </div>
          </label>
          
          <label class="relative flex items-start">
            <%= f.radio_button :schedule, 'weekly', class: "h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500" %>
            <div class="ml-3 text-sm">
              <span class="font-medium text-gray-700">Weekly</span>
              <p class="text-gray-500">Receive reports once a week</p>
            </div>
          </label>
          
          <label class="relative flex items-start">
            <%= f.radio_button :schedule, 'monthly', class: "h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500" %>
            <div class="ml-3 text-sm">
              <span class="font-medium text-gray-700">Monthly</span>
              <p class="text-gray-500">Receive reports once a month</p>
            </div>
          </label>
        </div>
      </div>

      <!-- Delivery Time -->
      <div id="delivery_time_field" style="display: none;">
        <%= f.label :delivery_time, "Preferred Delivery Time", class: "block text-sm font-medium text-gray-700" %>
        <%= f.time_field :delivery_time, 
            value: @delivery_preference.delivery_time || '09:00',
            class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
        <p class="mt-2 text-sm text-gray-500">Time when scheduled reports will be delivered</p>
      </div>

      <!-- Timezone -->
      <div>
        <%= f.label :timezone, class: "block text-sm font-medium text-gray-700" %>
        <%= f.time_zone_select :timezone, 
            ActiveSupport::TimeZone.us_zones, 
            { default: current_user.timezone || 'Eastern Time (US & Canada)' },
            class: "mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
      </div>

      <!-- Active Status -->
      <div class="relative flex items-start">
        <div class="flex h-5 items-center">
          <%= f.check_box :active, class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" %>
        </div>
        <div class="ml-3 text-sm">
          <%= f.label :active, class: "font-medium text-gray-700" do %>
            Active
          <% end %>
          <p class="text-gray-500">Enable this delivery preference immediately</p>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-end space-x-3 pt-6 border-t">
        <%= link_to "Cancel", delivery_preferences_path, class: "px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        <%= f.submit "Create Preference", class: "px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      </div>
    <% end %>
  </div>
</div>

<script>
  // Handle channel selection
  document.addEventListener('DOMContentLoaded', function() {
    const channelOptions = document.querySelectorAll('.channel-option');
    const formatField = document.getElementById('format_field');
    const formatSelect = document.querySelector('select[name="delivery_preference[format]"]');
    
    channelOptions.forEach(option => {
      const radio = option.querySelector('input[type="radio"]');
      
      radio.addEventListener('change', function() {
        // Update visual selection
        channelOptions.forEach(opt => {
          opt.classList.remove('ring-2', 'ring-indigo-500', 'border-indigo-500');
        });
        
        if (radio.checked) {
          option.classList.add('ring-2', 'ring-indigo-500', 'border-indigo-500');
          
          // Update format options based on channel
          const channel = radio.dataset.channel;
          updateFormatOptions(channel);
        }
      });
      
      // Set initial state
      if (radio.checked) {
        option.classList.add('ring-2', 'ring-indigo-500', 'border-indigo-500');
      }
    });
    
    function updateFormatOptions(channel) {
      const channelFormats = {
        'email': ['text', 'html', 'pdf'],
        'whatsapp': ['text', 'pdf'],
        'sms': ['text'],
        'pdf': ['pdf'],
        'slides': ['pptx']
      };
      
      const formats = channelFormats[channel] || ['text'];
      
      // Clear existing options
      formatSelect.innerHTML = '';
      
      // Add new options
      formats.forEach(format => {
        const option = document.createElement('option');
        option.value = format;
        option.text = format.charAt(0).toUpperCase() + format.slice(1);
        formatSelect.appendChild(option);
      });
    }
    
    // Handle schedule selection
    const scheduleRadios = document.querySelectorAll('input[name="delivery_preference[schedule]"]');
    const deliveryTimeField = document.getElementById('delivery_time_field');
    
    scheduleRadios.forEach(radio => {
      radio.addEventListener('change', function() {
        if (radio.value && radio.value !== '') {
          deliveryTimeField.style.display = 'block';
        } else {
          deliveryTimeField.style.display = 'none';
        }
      });
      
      // Set initial state
      if (radio.checked && radio.value && radio.value !== '') {
        deliveryTimeField.style.display = 'block';
      }
    });
  });
</script>