<% content_for :page_title, @template[:name] %>
<% content_for :page_subtitle, @template[:description] %>

<div class="dashboard-content">
  <section class="content-section active">

    <!-- Header Section -->
    <div class="section-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-32);">
      <div>
        <h1 class="section-title" style="font-size: var(--font-size-2xl); font-weight: var(--font-weight-bold); color: var(--color-text); margin: 0 0 var(--space-8) 0; line-height: var(--line-height-tight);"><%= @template[:name] %></h1>
        <p class="section-subtitle" style="font-size: var(--font-size-base); color: var(--color-text-secondary); margin: 0;"><%= @template[:description] %></p>
      </div>
      <div style="display: flex; align-items: center; gap: var(--space-12);">
        <%= link_to industry_templates_path, class: "btn btn--outline btn--sm" do %>
          <svg style="width: var(--space-16); height: var(--space-16); margin-right: var(--space-8);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Back to Templates
        <% end %>

        <%= form_with url: apply_industry_template_path(@template[:id]), method: :post, local: true, style: "display: inline;" do |form| %>
          <%= form.submit "Apply This Template", class: "btn btn--primary",
              confirm: "Apply #{@template[:name]} template to your dashboard?" %>
        <% end %>
      </div>
    </div>

    <!-- Template Preview Dashboard -->
    <div class="template-preview-dashboard">

      <!-- Preview Metrics Grid -->
      <div class="metrics-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--space-24); margin-bottom: var(--space-32);">
        <% @template[:metrics].first(4).each do |metric| %>
          <div class="metric-card" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); padding: var(--space-24); transition: all var(--duration-normal) var(--ease-standard);">
            <div class="metric-icon" style="margin-bottom: var(--space-16);">
              <svg style="width: var(--space-24); height: var(--space-24); color: var(--color-text-secondary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <% case metric[:icon] %>
                <% when 'dollar-sign' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                <% when 'shopping-bag' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z"/>
                <% when 'trending-up' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                <% when 'users' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                <% when 'activity' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                <% when 'clock' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                <% when 'star' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                <% else %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                <% end %>
              </svg>
            </div>
            <div class="metric-content">
              <h3 style="font-size: var(--font-size-sm, 12px) !important; color: var(--color-text-secondary, #6b7280) !important; font-weight: var(--font-weight-medium, 500) !important; margin: 0 0 var(--space-8, 8px) 0 !important; line-height: 1.2 !important;"><%= metric[:label] %></h3>
              <p class="metric-value" style="font-size: var(--font-size-3xl, 24px) !important; font-weight: var(--font-weight-bold, 600) !important; color: var(--color-text, #111827) !important; margin: 0 0 var(--space-4, 4px) 0 !important; line-height: 1.1 !important; font-variant-numeric: tabular-nums;">
                <% case metric[:format] %>
                <% when 'currency' %>
                  $<%= number_with_delimiter(@preview_data[metric[:key]] || 0) %>
                <% when 'percentage' %>
                  <%= @preview_data[metric[:key]] || 0 %>%
                <% when 'rating' %>
                  <%= @preview_data[metric[:key]] || 0 %>/5
                <% when 'decimal' %>
                  <%= number_with_precision(@preview_data[metric[:key]] || 0, precision: 1) %>
                <% else %>
                  <%= number_with_delimiter(@preview_data[metric[:key]] || 0) %>
                <% end %>
              </p>
              <p class="metric-change <%= @metric_trends[metric[:key]]&.dig(:direction) == 'up' ? 'positive' : 'negative' %>" style="font-size: var(--font-size-sm, 12px) !important; color: <%= @metric_trends[metric[:key]]&.dig(:direction) == 'up' ? 'var(--color-success, #10b981)' : 'var(--color-error, #ef4444)' %> !important; font-weight: var(--font-weight-medium, 500) !important; margin: 0 !important; line-height: 1.2 !important;">
                <%= @metric_trends[metric[:key]]&.dig(:direction) == 'up' ? '↑' : '↓' %> <%= @metric_trends[metric[:key]]&.dig(:trend) || 0 %>% vs last period
              </p>
            </div>
          </div>
        <% end %>
      </div>

      <!-- AI Insights Panel -->
      <div class="ai-insights-panel" style="margin-bottom: var(--space-32);">
        <h2 style="display: flex; align-items: center; gap: var(--space-8); margin: 0 0 var(--space-24) 0; font-size: var(--font-size-xl); font-weight: var(--font-weight-semibold); color: var(--color-text);">
          <svg style="width: var(--space-20); height: var(--space-20); color: var(--color-text-secondary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
          </svg>
          AI-Powered <%= @template[:category] %> Insights
        </h2>

        <div class="insights-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--space-20);">
          <% @template[:insights].each do |insight| %>
            <div class="insight-card <%= insight[:type] %>" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); padding: var(--space-20); position: relative; <%= case insight[:type]
              when 'critical' then 'border-left: 4px solid var(--color-error);'
              when 'opportunity' then 'border-left: 4px solid var(--color-warning);'
              else 'border-left: 4px solid var(--color-info);'
              end %>">
              <div class="insight-header" style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: var(--space-12);">
                <span class="insight-type" style="display: flex; align-items: center; font-size: var(--font-size-sm); font-weight: var(--font-weight-medium); color: var(--color-text-secondary);">
                  <svg style="width: var(--space-16); height: var(--space-16); margin-right: var(--space-8); color: <%= insight[:type] == 'critical' ? 'var(--color-error)' : insight[:type] == 'opportunity' ? 'var(--color-warning)' : 'var(--color-primary)' %>;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <% if insight[:type] == 'critical' %>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    <% elsif insight[:type] == 'opportunity' %>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    <% else %>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    <% end %>
                  </svg>
                  <%= insight[:title] %>
                </span>
                <span class="confidence-score" style="font-size: var(--font-size-xs); color: var(--color-text-secondary);"><%= rand(85..95) %>% confidence</span>
              </div>
              <p style="margin: 0 0 var(--space-16) 0; line-height: var(--line-height-relaxed); color: var(--color-text);"><%= insight[:message] %></p>
              <button class="btn btn--outline btn--sm">Learn More</button>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="charts-section" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: var(--space-24); margin-bottom: var(--space-32);">
        <% @template[:charts].each do |chart| %>
          <div class="chart-container" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); padding: var(--space-24);">
            <div class="chart-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-20);">
              <h3 style="display: flex; align-items: center; gap: var(--space-8); margin: 0; font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); color: var(--color-text);">
                <svg style="width: var(--space-20); height: var(--space-20); color: var(--color-text-secondary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
                <%= chart[:title] %>
              </h3>
              <div class="chart-controls" style="display: flex; gap: var(--space-8);">
                <button class="btn btn--outline btn--sm">Export</button>
              </div>
            </div>
            <div class="chart-wrapper" style="position: relative; height: var(--space-300); display: flex; align-items: center; justify-content: center; background-color: var(--color-background); border: 1px solid var(--color-card-border); border-radius: var(--radius-md);">
              <div style="height: 200px; position: relative;">
                <canvas id="chart-<%= chart[:id] %>" data-chart-type="<%= chart[:type] %>" data-chart-data="<%= @chart_data[chart[:id]].to_json %>"></canvas>
              </div>
            </div>
          </div>
        <% end %>
      </div>

    </div>

    <!-- Template Details -->
    <div style="margin-top: var(--space-48); display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: var(--space-32);">

      <!-- All Metrics -->
      <div class="chart-container" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); padding: var(--space-24);">
        <div class="chart-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-20);">
          <h3 style="margin: 0; font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); color: var(--color-text);">Included Metrics</h3>
        </div>
        <div style="display: flex; flex-direction: column; gap: var(--space-12);">
          <% @template[:metrics].each do |metric| %>
            <div style="display: flex; align-items: center; gap: var(--space-12); padding: var(--space-12); border-radius: var(--radius-md); background-color: var(--color-background); border: 1px solid var(--color-card-border);">
              <div style="width: var(--space-32); height: var(--space-32); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; background-color: var(--color-primary-light);">
                <svg style="width: var(--space-16); height: var(--space-16); color: var(--color-primary);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              <div>
                <p style="font-size: var(--font-size-base); font-weight: var(--font-weight-medium); color: var(--color-text); margin: 0 0 var(--space-4) 0;"><%= metric[:label] %></p>
                <p style="font-size: var(--font-size-sm); color: var(--color-text-secondary); margin: 0;">Format: <%= metric[:format].humanize %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Chart Types -->
      <div class="chart-container" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); padding: var(--space-24);">
        <div class="chart-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-20);">
          <h3 style="margin: 0; font-size: var(--font-size-lg); font-weight: var(--font-weight-semibold); color: var(--color-text);">Visualization Types</h3>
        </div>
        <div style="display: flex; flex-direction: column; gap: var(--space-12);">
          <% @template[:charts].each do |chart| %>
            <div style="display: flex; align-items: center; gap: var(--space-12); padding: var(--space-12); border-radius: var(--radius-md); background-color: var(--color-background); border: 1px solid var(--color-card-border);">
              <div style="width: var(--space-32); height: var(--space-32); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; background-color: var(--color-success-light);">
                <svg style="width: var(--space-16); height: var(--space-16); color: var(--color-success);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                </svg>
              </div>
              <div>
                <p style="font-size: var(--font-size-base); font-weight: var(--font-weight-medium); color: var(--color-text); margin: 0 0 var(--space-4) 0;"><%= chart[:title] %></p>
                <p style="font-size: var(--font-size-sm); color: var(--color-text-secondary); margin: 0;">Type: <%= chart[:type].humanize %></p>
              </div>
            </div>
          <% end %>
        </div>
      </div>

    </div>

  </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize all charts on the page
  const chartElements = document.querySelectorAll('[data-chart-type]');
  
  chartElements.forEach(element => {
    const chartType = element.dataset.chartType;
    const chartData = JSON.parse(element.dataset.chartData);
    const ctx = element.getContext('2d');
    
    // Configure chart based on type
    let config = {
      type: chartType,
      data: {
        labels: chartData.labels,
        datasets: chartData.datasets.map((dataset, index) => {
          // Add colors to datasets
          const colors = [
            'rgba(33, 128, 141, 1)',
            'rgba(94, 82, 64, 1)',
            'rgba(168, 75, 47, 1)',
            'rgba(98, 108, 113, 1)'
          ];
          
          return {
            ...dataset,
            borderColor: colors[index % colors.length],
            backgroundColor: chartType === 'line' ? 
              colors[index % colors.length].replace('1)', '0.1)') : 
              colors.map(c => c.replace('1)', '0.8)')),
            tension: 0.3
          };
        })
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: chartType === 'doughnut' || chartType === 'pie' ? 'right' : 'top',
            labels: {
              usePointStyle: true,
              font: { family: 'var(--font-family-base)' }
            }
          }
        }
      }
    };
    
    // Type-specific options
    if (chartType === 'line' || chartType === 'bar') {
      config.options.scales = {
        y: {
          beginAtZero: true,
          grid: { color: 'rgba(94, 82, 64, 0.1)' }
        },
        x: {
          grid: { color: 'rgba(94, 82, 64, 0.1)' }
        }
      };
    }
    
    new Chart(ctx, config);
  });
});
</script>
