<div class="chart-component">
  <% if config['title'].present? %>
    <h4 class="text-lg font-medium text-gray-900 mb-3"><%= config['title'] %></h4>
  <% end %>

  <div class="chart-placeholder bg-white border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
    <svg class="mx-auto w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
    </svg>
    
    <h4 class="text-lg font-medium text-gray-900 mb-2">
      <%= config['chart_type']&.humanize || 'Chart' %> Component
    </h4>
    
    <p class="text-gray-600 mb-4">
      Chart preview will be rendered here when data is connected.
    </p>

    <% if data.present? %>
      <div class="text-left bg-gray-50 rounded p-3 text-sm">
        <strong>Data Preview:</strong><br>
        <code class="text-xs text-gray-700">
          <%= truncate(data.to_json, length: 100) %>
        </code>
      </div>
    <% end %>

    <% if config.present? %>
      <div class="mt-3 text-left">
        <details class="text-sm">
          <summary class="cursor-pointer text-teal-600 hover:text-teal-700">Chart Configuration</summary>
          <div class="mt-2 bg-gray-50 rounded p-2 text-xs text-gray-700">
            <% config.each do |key, value| %>
              <div><strong><%= key.humanize %>:</strong> <%= value %></div>
            <% end %>
          </div>
        </details>
      </div>
    <% end %>
  </div>
</div>
