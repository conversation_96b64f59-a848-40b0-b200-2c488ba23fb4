<div class="text-component">
  <div class="prose prose-teal max-w-none">
    <% if config['title'].present? %>
      <h4 class="text-lg font-medium text-gray-900 mb-3"><%= config['title'] %></h4>
    <% end %>

    <% if data.present? %>
      <div class="text-gray-700">
        <%= simple_format(data) %>
      </div>
    <% elsif config['content'].present? %>
      <div class="text-gray-700">
        <%= simple_format(config['content']) %>
      </div>
    <% else %>
      <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <svg class="mx-auto w-8 h-8 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
        </svg>
        <h4 class="text-base font-medium text-gray-900 mb-2">Text Component</h4>
        <p class="text-gray-600">No content has been added to this text component yet.</p>
      </div>
    <% end %>

    <% if config['show_timestamp'] == true %>
      <div class="mt-4 text-xs text-gray-500 border-t border-gray-200 pt-2">
        Last updated: <%= Time.current.strftime("%B %d, %Y at %I:%M %p") %>
      </div>
    <% end %>

    <% if config.present? && config.keys.any? %>
      <div class="mt-3">
        <details class="text-sm">
          <summary class="cursor-pointer text-teal-600 hover:text-teal-700">Text Configuration</summary>
          <div class="mt-2 bg-gray-50 rounded p-2 text-xs text-gray-700">
            <% config.each do |key, value| %>
              <div><strong><%= key.humanize %>:</strong> <%= value %></div>
            <% end %>
          </div>
        </details>
      </div>
    <% end %>
  </div>
</div>
