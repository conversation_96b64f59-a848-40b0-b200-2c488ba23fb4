<div class="table-component">
  <% if config['title'].present? %>
    <h4 class="text-lg font-medium text-gray-900 mb-3"><%= config['title'] %></h4>
  <% end %>

  <div class="overflow-hidden bg-white border border-gray-200 rounded-lg">
    <% if data.present? && data.is_a?(Array) && data.any? %>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <% if data.first.is_a?(Hash) %>
                <% data.first.keys.first(10).each do |column| %>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <%= column.to_s.humanize %>
                  </th>
                <% end %>
              <% else %>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value
                </th>
              <% end %>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% data.first(config['max_rows']&.to_i || 10).each_with_index do |row, index| %>
              <tr class="<%= index.even? ? 'bg-white' : 'bg-gray-50' %>">
                <% if row.is_a?(Hash) %>
                  <% row.values.first(10).each do |value| %>
                    <td class="px-4 py-3 text-sm text-gray-900">
                      <%= truncate(value.to_s, length: 50) %>
                    </td>
                  <% end %>
                <% else %>
                  <td class="px-4 py-3 text-sm text-gray-900">
                    <%= truncate(row.to_s, length: 50) %>
                  </td>
                <% end %>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
      
      <% if data.length > (config['max_rows']&.to_i || 10) %>
        <div class="bg-gray-50 px-4 py-3 border-t border-gray-200">
          <p class="text-sm text-gray-600">
            Showing <%= config['max_rows'] || 10 %> of <%= data.length %> rows
          </p>
        </div>
      <% end %>
    <% else %>
      <div class="p-8 text-center">
        <svg class="mx-auto w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
        </svg>
        <h4 class="text-lg font-medium text-gray-900 mb-2">Table Component</h4>
        <p class="text-gray-600">No data available to display in table format.</p>
      </div>
    <% end %>
  </div>

  <% if config.present? && config.keys.any? %>
    <div class="mt-3">
      <details class="text-sm">
        <summary class="cursor-pointer text-teal-600 hover:text-teal-700">Table Configuration</summary>
        <div class="mt-2 bg-gray-50 rounded p-2 text-xs text-gray-700">
          <% config.each do |key, value| %>
            <div><strong><%= key.humanize %>:</strong> <%= value %></div>
          <% end %>
        </div>
      </details>
    </div>
  <% end %>
</div>
