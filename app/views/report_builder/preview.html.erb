<div class="report-container">
  <!-- Report Header -->
  <div class="report-header text-center">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">
      <%= @report&.name || "Untitled Report" %>
    </h1>
    <p class="text-gray-600 text-lg">
      Generated on <%= Date.current.strftime("%B %d, %Y") %>
    </p>
    <% if @report&.description.present? %>
      <p class="text-gray-700 mt-4 max-w-2xl mx-auto">
        <%= @report.description %>
      </p>
    <% end %>
  </div>

  <!-- Report Components -->
  <div class="report-content">
    <% if @components.present? %>
      <% @components.each_with_index do |component, index| %>
        <div class="report-component" data-component-id="<%= component['id'] %>">
          <%= render 'report_component', component: component, index: index %>
        </div>
      <% end %>
    <% else %>
      <div class="text-center py-12">
        <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Components Added</h3>
        <p class="text-gray-500 max-w-sm mx-auto">
          This report doesn't have any components yet. 
          <%= link_to "Go back to the editor", edit_report_builder_path(params[:id]), 
                      class: "text-teal-600 hover:text-teal-700 font-medium" %> 
          to add charts, tables, and other components.
        </p>
      </div>
    <% end %>
  </div>

  <!-- Report Data Summary -->
  <% if @report_data.present? %>
    <div class="mt-8 border-t border-gray-200 pt-8">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Data Sources</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <% @report_data.each do |source_name, data| %>
          <div class="bg-white border border-gray-200 rounded-lg p-4">
            <h3 class="font-medium text-gray-900 mb-2"><%= source_name.humanize %></h3>
            <p class="text-sm text-gray-600">
              <%= pluralize(data.is_a?(Array) ? data.length : 1, 'record') %>
            </p>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Report Footer -->
  <div class="mt-12 pt-8 border-t border-gray-200 text-center text-gray-500">
    <p class="text-sm">
      Report generated by Data Refinery Platform • 
      <%= link_to "datarefinery.com", root_path, class: "text-teal-600 hover:text-teal-700" %>
    </p>
  </div>
</div>
