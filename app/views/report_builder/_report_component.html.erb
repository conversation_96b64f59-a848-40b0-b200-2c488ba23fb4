<% 
  # Extract component properties
  component_type = component['type'] || 'unknown'
  component_config = component['config'] || {}
  component_data = component['data'] || {}
%>

<div class="component-wrapper">
  <h3 class="component-title">
    <span class="component-number"><%= index + 1 %>.</span>
    <%= component['name'] || component_type.humanize %>
  </h3>

  <% case component_type %>
  <% when 'chart' %>
    <%= render 'components/chart_component', component: component, config: component_config, data: component_data %>
  
  <% when 'table' %>
    <%= render 'components/table_component', component: component, config: component_config, data: component_data %>
  
  <% when 'metric' %>
    <%= render 'components/metric_component', component: component, config: component_config, data: component_data %>
  
  <% when 'text' %>
    <%= render 'components/text_component', component: component, config: component_config, data: component_data %>
  
  <% else %>
    <!-- Generic component fallback -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div class="flex items-center">
        <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-yellow-800 font-medium">Unknown Component Type: <%= component_type %></span>
      </div>
      <div class="mt-2 text-sm text-yellow-700">
        This component type is not yet supported in preview mode.
      </div>
    </div>
  <% end %>

  <% if component['description'].present? %>
    <div class="mt-3 text-sm text-gray-600">
      <%= component['description'] %>
    </div>
  <% end %>
</div>
