<%
  colors = pipeline_status_colors(pipeline.status)
  progress = pipeline.progress_percentage || 0
  duration = pipeline.started_at ? time_ago_in_words(pipeline.started_at) : 'N/A'
  records_processed = pipeline.records_processed || 0
  total_records = pipeline.total_records || 0
  
  # Get latest metrics
  latest_metric = pipeline.pipeline_metrics.order(recorded_at: :desc).first
  cpu_usage = latest_metric&.cpu_usage || 0
  memory_usage = latest_metric&.memory_usage_gb || 0
  records_per_second = latest_metric&.records_per_second || 0
%>

<div class="border rounded-lg p-4 transition-all duration-200 hover:shadow-md <%= colors[:border] %>" 
     style="background-color: <%= pipeline.status == 'failed' ? 'var(--color-error-light)' : 'var(--color-surface)' %>;">
  <div class="flex items-start justify-between mb-3">
    <div>
      <div class="flex items-center gap-3">
        <h4 class="font-semibold" style="color: var(--color-text);">
          <%= pipeline.data_source&.name || 'Unknown Pipeline' %>
        </h4>
        <span class="px-2 py-1 rounded text-xs font-medium <%= colors[:bg] %> <%= colors[:text] %>">
          <%= pipeline.status.humanize %>
        </span>
      </div>
      <p class="text-sm mt-1" style="color: var(--color-text-secondary);">
        <%= pipeline.data_source&.source_type&.humanize || 'Unknown' %> → 
        <%= pipeline.destination_type&.humanize || 'Data Warehouse' %>
      </p>
    </div>
    <div class="flex items-center gap-2">
      <% if pipeline.status == 'running' %>
        <button class="p-1.5 rounded transition-colors hover:bg-gray-100" data-pipeline-id="<%= pipeline.id %>">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </button>
      <% elsif pipeline.status == 'failed' %>
        <button class="p-1.5 rounded transition-colors hover:bg-red-100" data-pipeline-id="<%= pipeline.id %>">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
        </button>
      <% end %>
      <%= link_to pipeline_execution_path(pipeline), class: "p-1.5 rounded transition-colors hover:bg-gray-100" do %>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"/>
        </svg>
      <% end %>
    </div>
  </div>

  <% if pipeline.status == 'running' && progress > 0 %>
    <div class="space-y-3">
      <div>
        <div class="flex items-center justify-between text-sm mb-1">
          <span style="color: var(--color-text-secondary);">Progress</span>
          <span class="font-medium" style="color: var(--color-text);">
            <%= progress.round(1) %>% • 
            <%= number_with_delimiter(records_processed) %> / 
            <%= number_with_delimiter(total_records) %> records
          </span>
        </div>
        <div class="w-full rounded-full h-2 overflow-hidden" style="background-color: var(--color-surface-secondary);">
          <div class="h-2 rounded-full transition-all duration-500 progress-bar" 
               style="width: <%= progress %>%; background: linear-gradient(to right, var(--color-success), var(--color-success-dark));"></div>
        </div>
      </div>

      <div class="grid grid-cols-5 gap-3 text-sm">
        <div>
          <p style="color: var(--color-text-secondary);">Duration</p>
          <p class="font-medium" style="color: var(--color-text);"><%= duration %></p>
        </div>
        <div>
          <p style="color: var(--color-text-secondary);">Speed</p>
          <p class="font-medium" style="color: var(--color-text);"><%= number_with_delimiter(records_per_second) %>/s</p>
        </div>
        <div>
          <p style="color: var(--color-text-secondary);">ETA</p>
          <p class="font-medium" style="color: var(--color-text);">
            <% if records_per_second > 0 && total_records > records_processed %>
              <% eta_seconds = (total_records - records_processed) / records_per_second %>
              <%= distance_of_time_in_words(eta_seconds.seconds) %>
            <% else %>
              N/A
            <% end %>
          </p>
        </div>
        <div>
          <p style="color: var(--color-text-secondary);">CPU</p>
          <p class="font-medium" style="color: var(--color-text);"><%= cpu_usage.round(1) %>%</p>
        </div>
        <div>
          <p style="color: var(--color-text-secondary);">Memory</p>
          <p class="font-medium" style="color: var(--color-text);"><%= memory_usage.round(1) %>GB</p>
        </div>
      </div>
    </div>
  <% elsif pipeline.status == 'pending' %>
    <div class="flex items-center gap-2 text-sm" style="color: var(--color-text-secondary);">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
      <span>Waiting to start...</span>
    </div>
  <% elsif pipeline.status == 'failed' %>
    <div class="rounded p-3 text-sm mb-3" style="background-color: var(--color-error-light);">
      <p class="font-medium mb-1" style="color: var(--color-error);">
        Error: <%= pipeline.error_message || 'Unknown error' %>
      </p>
      <p class="text-xs" style="color: var(--color-error-dark);">
        <%= pipeline.error_details || 'Check logs for more details' %>
      </p>
    </div>
    <div class="flex items-center justify-between text-sm">
      <span style="color: var(--color-text-secondary);">
        Failed at: <%= pipeline.completed_at&.strftime('%l:%M:%S %p') || 'Unknown' %>
      </span>
      <%= link_to "View Full Logs →", "#", class: "font-medium", style: "color: var(--color-error);" %>
    </div>
  <% end %>
</div>
