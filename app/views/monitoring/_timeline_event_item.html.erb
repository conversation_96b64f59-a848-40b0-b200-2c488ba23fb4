<%
  icon_class = case event.event_type
               when 'pipeline_completed' then 'timeline-event-icon--success'
               when 'pipeline_failed' then 'timeline-event-icon--error'
               when 'resource_alert' then 'timeline-event-icon--warning'
               else 'timeline-event-icon--info'
               end
%>

<div class="timeline-event-item">
  <div class="timeline-event-icon <%= icon_class %>">
    <% if event.event_type == 'pipeline_completed' %>
      <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" style="color: #10b981;">
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
      </svg>
    <% elsif event.event_type == 'pipeline_failed' %>
      <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" style="color: #ef4444;">
        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
      </svg>
    <% elsif event.event_type == 'resource_alert' %>
      <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" style="color: #f59e0b;">
        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
      </svg>
    <% end %>
  </div>
  
  <div class="timeline-event-content">
    <span class="timeline-event-time"><%= event.occurred_at.strftime('%l:%M %p') %></span>
    <p class="font-medium text-gray-900 text-sm"><%= event.title %></p>
    <p class="text-sm text-gray-600 mt-1"><%= event.description %></p>
    <% if event.metadata.present? && event.metadata['details'] %>
      <p class="text-xs text-gray-500 mt-2"><%= event.metadata['details'] %></p>
    <% end %>
  </div>
</div>
