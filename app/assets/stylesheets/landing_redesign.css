/* Landing Page Redesign - Premium Design System */

/* ===========================
   Landing Page Layout
   =========================== */

.landing-page {
  font-family: var(--font-family-base);
  color: var(--color-text);
  background: var(--color-background);
  line-height: var(--line-height-normal);
}

/* ===========================
   Hero Section
   =========================== */

.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-surface);
  padding: var(--space-64) var(--space-24);
}

.hero-container {
  position: relative;
  z-index: 10;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

.hero-content {
  text-align: center;
  margin-bottom: var(--space-32);
}

/* Brand Section */
.brand-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-32);
}

.brand-logo {
  display: flex;
  height: 56px;
  width: 56px;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: var(--color-primary);
  box-shadow: var(--shadow-lg);
  margin-right: var(--space-16);
}

.brand-icon {
  height: 32px;
  width: 32px;
  color: var(--color-surface);
}

.brand-name {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--space-24);
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: var(--color-success);
  border-radius: var(--radius-full);
  margin-right: var(--space-8);
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.status-icon {
  margin-left: var(--space-8);
  height: 16px;
  width: 16px;
  color: var(--color-primary);
}

/* Headlines */
.hero-headline {
  font-size: clamp(var(--font-size-4xl), 8vw, 72px);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-24);
  line-height: var(--line-height-tight);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.hero-highlight {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.hero-subheadline {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-40);
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
  line-height: var(--line-height-normal);
}

/* ===========================
   Dashboard Preview
   =========================== */

.dashboard-preview {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--space-48);
  box-shadow: var(--shadow-2xl);
  max-width: 1200px;
  margin: 0 auto var(--space-48) auto;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-24);
}

.dashboard-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.dashboard-status {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.dashboard-status .status-indicator {
  margin-left: var(--space-8);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-24);
}

.metric-card {
  text-align: center;
  padding: var(--space-16);
  border-radius: var(--radius-lg);
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.metric-card--revenue {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.05) 100%);
  border-color: rgba(16, 185, 129, 0.2);
}

.metric-card--orders {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(37, 99, 235, 0.05) 100%);
  border-color: rgba(59, 130, 246, 0.2);
}

.metric-card--customers {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(124, 58, 237, 0.05) 100%);
  border-color: rgba(139, 92, 246, 0.2);
}

.metric-card--conversion {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(217, 119, 6, 0.05) 100%);
  border-color: rgba(245, 158, 11, 0.2);
}

.metric-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-8);
}

.metric-card--revenue .metric-value { color: #10b981; }
.metric-card--orders .metric-value { color: #3b82f6; }
.metric-card--customers .metric-value { color: #8b5cf6; }
.metric-card--conversion .metric-value { color: #f59e0b; }

.metric-label {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-4);
}

.metric-change {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.metric-change--positive {
  color: var(--color-success);
}

/* AI Insight Card */
.ai-insight-card {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-16);
}

.ai-insight-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-12);
}

.ai-insight-icon {
  height: 20px;
  width: 20px;
  color: #3b82f6;
  margin-right: var(--space-8);
}

.ai-insight-label {
  font-weight: var(--font-weight-medium);
  color: #1e40af;
}

.ai-insight-confidence {
  margin-left: auto;
  font-size: var(--font-size-xs);
  color: #3b82f6;
}

.ai-insight-text {
  color: #1e40af;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/* ===========================
   CTA Section
   =========================== */

.cta-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
  justify-content: center;
  align-items: center;
  margin-bottom: var(--space-40);
}

@media (min-width: 640px) {
  .cta-section {
    flex-direction: row;
  }
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) var(--space-24);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--ease-standard);
  text-decoration: none;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn--large {
  padding: var(--space-16) var(--space-32);
  font-size: var(--font-size-lg);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-surface);
  box-shadow: var(--shadow-lg);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.btn--outline {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 2px solid var(--color-border);
  color: var(--color-text);
  box-shadow: var(--shadow-md);
}

.btn--outline:hover {
  background: var(--color-surface);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-icon {
  height: 20px;
  width: 20px;
  margin-left: var(--space-8);
}

.btn-icon--left {
  margin-left: 0;
  margin-right: var(--space-8);
}

.btn-subtitle {
  margin-left: var(--space-8);
  font-size: var(--font-size-sm);
  opacity: 0.7;
}

/* ===========================
   Trust Indicators
   =========================== */

.trust-indicators {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: var(--space-32);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.trust-indicator {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: var(--space-12) var(--space-16);
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.trust-icon {
  height: 16px;
  width: 16px;
  color: var(--color-success);
  margin-right: var(--space-8);
}

/* ===========================
   Section Layouts
   =========================== */

.section-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-24);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-48);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-32);
}

/* ===========================
   Trusted By Section
   =========================== */

.trusted-by-section {
  padding: var(--space-64) 0;
  background: var(--color-secondary);
}

.trusted-brands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-32);
  align-items: center;
  justify-items: center;
}

.trusted-brand {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  opacity: 0.7;
  transition: opacity var(--duration-normal) var(--ease-standard);
}

.trusted-brand:hover {
  opacity: 1;
}

/* ===========================
   Stats Section
   =========================== */

.stats-section {
  padding: var(--space-64) 0;
  background: var(--color-surface);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-32);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: clamp(var(--font-size-3xl), 5vw, var(--font-size-4xl));
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.stat-label {
  color: var(--color-text-secondary);
  font-size: var(--font-size-base);
}

/* ===========================
   Glass Morphism Effects
   =========================== */

.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
}

/* ===========================
   Responsive Design
   =========================== */

@media (max-width: 768px) {
  .hero-section {
    padding: var(--space-32) var(--space-16);
  }

  .dashboard-preview {
    padding: var(--space-24);
    margin-bottom: var(--space-32);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-16);
  }

  .trusted-brands-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-16);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-24);
  }

  .trust-indicators {
    flex-direction: column;
    gap: var(--space-16);
  }
}

@media (max-width: 480px) {
  .brand-section {
    flex-direction: column;
    gap: var(--space-16);
  }

  .brand-logo {
    margin-right: 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .trusted-brands-grid {
    grid-template-columns: 1fr;
  }
}

/* ===========================
   Accessibility
   =========================== */

@media (prefers-reduced-motion: reduce) {
  .btn,
  .metric-card,
  .trusted-brand {
    transition: none;
  }

  .btn:hover,
  .metric-card:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .glass-card,
  .dashboard-preview,
  .status-badge,
  .btn--outline {
    border-width: 2px;
    border-color: var(--color-text);
  }

  .btn--primary {
    background: var(--color-text);
    color: var(--color-surface);
  }
}
