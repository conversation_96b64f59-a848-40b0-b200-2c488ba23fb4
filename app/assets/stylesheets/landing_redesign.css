/* Landing Page Redesign - Premium Design System */

/* ===========================
   Landing Page Layout
   =========================== */

.landing-page {
  font-family: var(--font-family-base);
  color: var(--color-text);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  line-height: var(--line-height-normal);
}

/* ===========================
   Hero Section
   =========================== */

.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  padding: var(--space-64) var(--space-24);
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.3) 0%, transparent 50%);
  z-index: 1;
}

.hero-container {
  position: relative;
  z-index: 10;
  max-width: 1280px;
  margin: 0 auto;
  width: 100%;
}

.hero-content {
  text-align: center;
  margin-bottom: var(--space-32);
}

/* Brand Section */
.brand-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-32);
}

.brand-logo {
  display: flex;
  height: 56px;
  width: 56px;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
  margin-right: var(--space-16);
}

.brand-icon {
  height: 32px;
  width: 32px;
  color: white;
}

.brand-name {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-12) var(--space-20);
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(16, 185, 129, 0.3);
  box-shadow: 0 8px 32px rgba(16, 185, 129, 0.2);
  margin-bottom: var(--space-24);
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: var(--radius-full);
  margin-right: var(--space-8);
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: #065f46;
}

.status-icon {
  margin-left: var(--space-8);
  height: 16px;
  width: 16px;
  color: #3b82f6;
}

/* Headlines */
.hero-headline {
  font-size: clamp(var(--font-size-4xl), 8vw, 72px);
  font-weight: var(--font-weight-bold);
  color: white;
  margin-bottom: var(--space-24);
  line-height: var(--line-height-tight);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-highlight {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  text-shadow: none;
}

.hero-subheadline {
  font-size: var(--font-size-xl);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-40);
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
  line-height: var(--line-height-normal);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ===========================
   Dashboard Preview
   =========================== */

.dashboard-preview {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-xl);
  padding: var(--space-48);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 1200px;
  margin: 0 auto var(--space-48) auto;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-24);
}

.dashboard-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
}

.dashboard-status {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.dashboard-status .status-indicator {
  margin-left: var(--space-8);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-24);
  margin-bottom: var(--space-24);
}

.metric-card {
  text-align: center;
  padding: var(--space-16);
  border-radius: var(--radius-lg);
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-normal) var(--ease-standard);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.metric-card--revenue {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.15) 100%);
  border-color: rgba(16, 185, 129, 0.4);
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.2);
}

.metric-card--orders {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(37, 99, 235, 0.15) 100%);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.metric-card--customers {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15) 0%, rgba(124, 58, 237, 0.15) 100%);
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.2);
}

.metric-card--conversion {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.15) 100%);
  border-color: rgba(245, 158, 11, 0.4);
  box-shadow: 0 4px 20px rgba(245, 158, 11, 0.2);
}

.metric-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-8);
}

.metric-card--revenue .metric-value { color: #10b981; }
.metric-card--orders .metric-value { color: #3b82f6; }
.metric-card--customers .metric-value { color: #8b5cf6; }
.metric-card--conversion .metric-value { color: #f59e0b; }

.metric-label {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-4);
}

.metric-change {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.metric-change--positive {
  color: var(--color-success);
}

/* AI Insight Card */
.ai-insight-card {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.2) 100%);
  border: 1px solid rgba(59, 130, 246, 0.4);
  border-radius: var(--radius-lg);
  padding: var(--space-16);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
}

.ai-insight-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-12);
}

.ai-insight-icon {
  height: 20px;
  width: 20px;
  color: #3b82f6;
  margin-right: var(--space-8);
}

.ai-insight-label {
  font-weight: var(--font-weight-medium);
  color: #1e40af;
}

.ai-insight-confidence {
  margin-left: auto;
  font-size: var(--font-size-xs);
  color: #3b82f6;
}

.ai-insight-text {
  color: #1e40af;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/* ===========================
   CTA Section
   =========================== */

.cta-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
  justify-content: center;
  align-items: center;
  margin-bottom: var(--space-40);
}

@media (min-width: 640px) {
  .cta-section {
    flex-direction: row;
  }
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) var(--space-24);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--ease-standard);
  text-decoration: none;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn--large {
  padding: var(--space-16) var(--space-32);
  font-size: var(--font-size-lg);
}

.btn--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

.btn--primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(59, 130, 246, 0.5);
}

.btn--outline {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 2px solid rgba(59, 130, 246, 0.3);
  color: #1e40af;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.btn--outline:hover {
  background: white;
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.btn-icon {
  height: 20px;
  width: 20px;
  margin-left: var(--space-8);
}

.btn-icon--left {
  margin-left: 0;
  margin-right: var(--space-8);
}

.btn-subtitle {
  margin-left: var(--space-8);
  font-size: var(--font-size-sm);
  opacity: 0.7;
}

/* ===========================
   Trust Indicators
   =========================== */

.trust-indicators {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: var(--space-32);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.trust-indicator {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: var(--space-12) var(--space-16);
  border-radius: var(--radius-md);
  border: 1px solid rgba(16, 185, 129, 0.2);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
  transition: all var(--duration-normal) var(--ease-standard);
}

.trust-indicator:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.15);
}

.trust-icon {
  height: 16px;
  width: 16px;
  color: #10b981;
  margin-right: var(--space-8);
}

/* ===========================
   Section Layouts
   =========================== */

.section-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-24);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-48);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-32);
}

/* ===========================
   Trusted By Section
   =========================== */

.trusted-by-section {
  padding: var(--space-64) 0;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.trusted-brands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-32);
  align-items: center;
  justify-items: center;
}

.trusted-brand {
  color: #475569;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  opacity: 0.8;
  transition: all var(--duration-normal) var(--ease-standard);
}

.trusted-brand:hover {
  opacity: 1;
  color: #334155;
  transform: translateY(-2px);
}

/* ===========================
   Stats Section
   =========================== */

.stats-section {
  padding: var(--space-64) 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-32);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: clamp(var(--font-size-3xl), 5vw, var(--font-size-4xl));
  font-weight: var(--font-weight-bold);
  color: #1e293b;
  margin-bottom: var(--space-8);
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  color: #64748b;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* ===========================
   Glass Morphism Effects
   =========================== */

.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
}

/* ===========================
   Responsive Design
   =========================== */

@media (max-width: 768px) {
  .hero-section {
    padding: var(--space-32) var(--space-16);
  }

  .dashboard-preview {
    padding: var(--space-24);
    margin-bottom: var(--space-32);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--space-16);
  }

  .trusted-brands-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-16);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-24);
  }

  .trust-indicators {
    flex-direction: column;
    gap: var(--space-16);
  }
}

@media (max-width: 480px) {
  .brand-section {
    flex-direction: column;
    gap: var(--space-16);
  }

  .brand-logo {
    margin-right: 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .trusted-brands-grid {
    grid-template-columns: 1fr;
  }
}

/* ===========================
   Accessibility
   =========================== */

@media (prefers-reduced-motion: reduce) {
  .btn,
  .metric-card,
  .trusted-brand {
    transition: none;
  }

  .btn:hover,
  .metric-card:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .glass-card,
  .dashboard-preview,
  .status-badge,
  .btn--outline {
    border-width: 2px;
    border-color: var(--color-text);
  }

  .btn--primary {
    background: var(--color-text);
    color: var(--color-surface);
  }
}
