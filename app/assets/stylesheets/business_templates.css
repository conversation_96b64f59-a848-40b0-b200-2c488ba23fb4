/* Business Templates Styles */

/* Enhanced Glass Card Styling */
.glass-card {
  padding: var(--space-32);
  margin-bottom: var(--space-24);
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(31, 38, 135, 0.15),
              0 10px 10px -5px rgba(31, 38, 135, 0.08);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Responsive Grid for Template Details */
.responsive-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-32);
}

/* Clean up inline styles with utility classes */
.template-feature-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-12);
  padding: var(--space-8);
  border-radius: var(--radius-md);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.template-feature-item:hover {
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.template-insight-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-12);
  padding: var(--space-12);
  border-radius: var(--radius-lg);
  background: rgba(var(--color-primary-rgb), 0.03);
  border: 1px solid rgba(var(--color-primary-rgb), 0.1);
  transition: all var(--duration-fast) var(--ease-standard);
}

.template-insight-item:hover {
  background: rgba(var(--color-primary-rgb), 0.08);
  border-color: rgba(var(--color-primary-rgb), 0.2);
  transform: translateY(-1px);
}

.template-benefit-stat {
  text-align: center;
  padding: var(--space-20);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all var(--duration-fast) var(--ease-standard);
}

.template-benefit-stat:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* Support Links */
.template-support-link {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  color: var(--color-primary);
  text-decoration: none;
  padding: var(--space-12);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
  border: 1px solid transparent;
}

.template-support-link:hover {
  background: var(--color-primary-light);
  border-color: rgba(var(--color-primary-rgb), 0.2);
  transform: translateX(4px);
  color: var(--color-primary);
}

.template-support-link:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Mobile Responsive Adjustments */
@media (max-width: 1024px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: var(--space-24);
  }

  .glass-card {
    padding: var(--space-24);
  }
}

@media (max-width: 768px) {
  .glass-card {
    padding: var(--space-20);
  }

  .template-feature-item,
  .template-insight-item {
    padding: var(--space-8);
  }

  .template-benefit-stat {
    padding: var(--space-16);
  }
}

/* ===========================
   2. Content Layout Components
   =========================== */

/* Feature List Styling */
.feature-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-12);
  padding: var(--space-12);
  border-radius: var(--radius-md);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.feature-item:hover {
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.feature-icon {
  width: 24px;
  height: 24px;
  color: var(--color-success);
  margin-top: 2px;
  flex-shrink: 0;
}

.feature-text {
  color: var(--color-text);
  line-height: var(--line-height-normal);
  font-size: var(--font-size-base);
}

/* Insights Grid Layout */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-20);
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-12);
  padding: var(--space-16);
  border-radius: var(--radius-lg);
  background: rgba(var(--color-primary-rgb), 0.03);
  border: 1px solid rgba(var(--color-primary-rgb), 0.1);
  transition: all var(--duration-fast) var(--ease-standard);
}

.insight-item:hover {
  background: rgba(var(--color-primary-rgb), 0.08);
  border-color: rgba(var(--color-primary-rgb), 0.2);
  transform: translateY(-2px);
}

.insight-icon-wrapper {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--color-primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.insight-icon {
  width: 20px;
  height: 20px;
  color: var(--color-primary);
}

.insight-text {
  color: var(--color-text);
  line-height: var(--line-height-normal);
  font-size: var(--font-size-base);
  flex: 1;
}

/* Benefits Statistics Grid */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-20);
}

.benefit-stat {
  text-align: center;
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all var(--duration-fast) var(--ease-standard);
}

.benefit-stat:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.benefit-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-8);
  line-height: var(--line-height-tight);
}

.benefit-label {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

/* Mobile Responsive Adjustments */
@media (max-width: 1024px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: var(--space-24);
  }

  .dashboard-content {
    padding: var(--space-24);
  }

  .glass-card {
    padding: var(--space-24);
  }

  .insights-grid {
    grid-template-columns: 1fr;
    gap: var(--space-16);
  }

  .benefits-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-16);
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: var(--space-16);
  }

  .glass-card {
    padding: var(--space-20);
  }

  .glass-card h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-16);
  }

  .glass-card h3 {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-12);
  }

  .feature-list {
    gap: var(--space-12);
  }

  .feature-item {
    padding: var(--space-8);
  }

  .insights-grid {
    gap: var(--space-12);
  }

  .insight-item {
    padding: var(--space-12);
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .benefit-stat {
    padding: var(--space-16);
  }

  .benefit-number {
    font-size: var(--font-size-2xl);
  }

  .header-title-group {
    flex-direction: column;
    align-items: flex-start !important;
    gap: var(--space-16);
  }

  .header-icon-wrapper {
    align-self: center;
  }

  .header-text {
    text-align: center;
    width: 100%;
  }
}

/* ===========================
   3. Interactive Elements
   =========================== */

/* Enhanced Glass Card Hover Effects */
.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(31, 38, 135, 0.15),
              0 10px 10px -5px rgba(31, 38, 135, 0.08);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Integration Card Styling */
.integration-card {
  display: flex;
  align-items: center;
  padding: var(--space-16);
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  transition: all var(--duration-fast) var(--ease-standard);
}

.integration-card:hover {
  background: rgba(var(--color-primary-rgb), 0.05);
  border-color: rgba(var(--color-primary-rgb), 0.2);
  transform: translateY(-1px);
}

.integration-icon {
  width: 20px;
  height: 20px;
  color: var(--color-success);
  margin-right: var(--space-12);
  flex-shrink: 0;
}

.integration-text {
  color: var(--color-text);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* Support Link Styling */
.support-link {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  color: var(--color-primary);
  text-decoration: none;
  padding: var(--space-12);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--ease-standard);
  border: 1px solid transparent;
}

.support-link:hover {
  background: var(--color-primary-light);
  border-color: rgba(var(--color-primary-rgb), 0.2);
  transform: translateX(4px);
  color: var(--color-primary);
}

.support-link:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.support-link svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* Dashboard Preview Card */
.dashboard-preview {
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  position: relative;
  overflow: hidden;
}

.preview-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  padding: var(--space-16);
  margin-bottom: var(--space-16);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-12);
}

.preview-title {
  color: white;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
}

.preview-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-sm);
}

.preview-value {
  color: white;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-4);
}

.preview-change {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-sm);
}

.preview-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
}

.preview-metric {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: var(--space-12);
  text-align: center;
}

.preview-metric-value {
  color: white;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-4);
}

.preview-metric-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-xs);
}

.preview-badge {
  position: absolute;
  bottom: var(--space-12);
  right: var(--space-12);
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--font-size-xs);
  background: rgba(255, 255, 255, 0.1);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
}

/* Loading State for Template Application */
.template-applying {
  opacity: 0.7;
  pointer-events: none;
}

.template-applying .btn {
  position: relative;
}

.template-applying .btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Template Card Specific Styles */
.template-card {
  transition: all var(--duration-normal) var(--ease-standard);
}

.template-card:hover {
  transform: translateY(-4px);
}

/* Button Hover Enhancements */
.btn:hover {
  transform: translateY(-1px);
}

/* Focus States for Accessibility */
.btn:focus,
input:focus,
select:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass-card {
    border-width: 2px;
    border-color: var(--color-border);
  }
  
  .btn {
    border-width: 2px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .glass-card,
  .template-card,
  .btn,
  .hover-lift {
    transition: none;
    transform: none;
  }
  
  .glass-card:hover,
  .template-card:hover,
  .btn:hover,
  .hover-lift:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .glass-card {
    background: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
  
  .btn {
    display: none !important;
  }
  
  .header-icon-gradient {
    background: #f3f4f6 !important;
  }
}
