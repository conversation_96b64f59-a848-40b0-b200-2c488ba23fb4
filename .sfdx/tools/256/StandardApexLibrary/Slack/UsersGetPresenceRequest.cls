global class UsersGetPresenceRequest {
	global static Slack.UsersGetPresenceRequest.Builder builder() { }
	global Object clone() { }
	global String getUser() { }
	global String toString() { }
global class Builder {
	global UsersGetPresenceRequest.Builder() { }
	global Slack.UsersGetPresenceRequest build() { }
	global Object clone() { }
	global Slack.UsersGetPresenceRequest.Builder user(String user) { }

}

}