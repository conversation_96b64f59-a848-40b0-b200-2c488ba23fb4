global class UserGroup {
	global Usergroup() { }
	global Object clone() { }
	global String getAutoType() { }
	global Integer getChannelCount() { }
	global String getCreatedBy() { }
	global Integer getDateCreate() { }
	global Integer getDateDelete() { }
	global Integer getDateUpdate() { }
	global String getDeletedBy() { }
	global String getDescription() { }
	global String getEnterpriseSubteamId() { }
	global String getHandle() { }
	global String getId() { }
	global String getName() { }
	global Slack.Usergroup.Prefs getPrefs() { }
	global String getTeamId() { }
	global String getUpdatedBy() { }
	global Integer getUserCount() { }
	global List<String> getUsers() { }
	global Boolean isAutoProvision() { }
	global Boolean isExternal() { }
	global Boolean isSubteam() { }
	global Boolean isUsergroup() { }
	global void setAutoProvision(Boolean autoProvision) { }
	global void setAutoType(String autoType) { }
	global void setChannelCount(Integer channelCount) { }
	global void setCreatedBy(String createdBy) { }
	global void setDateCreate(Integer dateCreate) { }
	global void setDateDelete(Integer dateDelete) { }
	global void setDateUpdate(Integer dateUpdate) { }
	global void setDeletedBy(String deletedBy) { }
	global void setDescription(String description) { }
	global void setEnterpriseSubteamId(String enterpriseSubteamId) { }
	global void setExternal(Boolean external) { }
	global void setHandle(String handle) { }
	global void setId(String id) { }
	global void setName(String name) { }
	global void setPrefs(Slack.Usergroup.Prefs prefs) { }
	global void setSubteam(Boolean subteam) { }
	global void setTeamId(String teamId) { }
	global void setUpdatedBy(String updatedBy) { }
	global void setUserCount(Integer userCount) { }
	global void setUsergroup(Boolean usergroup) { }
	global void setUsers(List<String> users) { }
	global String toString() { }

}