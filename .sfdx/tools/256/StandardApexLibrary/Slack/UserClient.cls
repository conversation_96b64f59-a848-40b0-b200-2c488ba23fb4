global class UserClient {
	global Slack.ApiTestResponse apiTest(Slack.ApiTestRequest req) { }
	global Slack.AppsEventAuthorizationsListResponse appsEventAuthorizationsList(Slack.AppsEventAuthorizationsListRequest req) { }
	global Slack.AppsUninstallResponse appsUninstall(Slack.AppsUninstallRequest req) { }
	global Slack.AuthRevokeResponse authRevoke(Slack.AuthRevokeRequest req) { }
	global Slack.AuthTeamsListResponse authTeamsList(Slack.AuthTeamsListRequest req) { }
	global Slack.AuthTestResponse authTest(Slack.AuthTestRequest req) { }
	global Slack.BookmarksAddResponse bookmarksAdd(Slack.BookmarksAddRequest req) { }
	global Slack.BookmarksEditResponse bookmarksEdit(Slack.BookmarksEditRequest req) { }
	global Slack.BookmarksListResponse bookmarksList(Slack.BookmarksListRequest req) { }
	global Slack.BookmarksRemoveResponse bookmarksRemove(Slack.BookmarksRemoveRequest req) { }
	global Slack.BotsInfoResponse botsInfo(Slack.BotsInfoRequest req) { }
	global Slack.CallsAddResponse callsAdd(Slack.CallsAddRequest req) { }
	global Slack.CallsEndResponse callsEnd(Slack.CallsEndRequest req) { }
	global Slack.CallsInfoResponse callsInfo(Slack.CallsInfoRequest req) { }
	global Slack.CallsParticipantsAddResponse callsParticipantsAdd(Slack.CallsParticipantsAddRequest req) { }
	global Slack.CallsParticipantsRemoveResponse callsParticipantsRemove(Slack.CallsParticipantsRemoveRequest req) { }
	global Slack.CallsUpdateResponse callsUpdate(Slack.CallsUpdateRequest req) { }
	global Slack.ChatDeleteResponse chatDelete(Slack.ChatDeleteRequest req) { }
	global Slack.ChatDeleteScheduledMessageResponse chatDeleteScheduledMessage(Slack.ChatDeleteScheduledMessageRequest req) { }
	global Slack.ChatGetPermalinkResponse chatGetPermalink(Slack.ChatGetPermalinkRequest req) { }
	global Slack.ChatMeMessageResponse chatMeMessage(Slack.ChatMeMessageRequest req) { }
	global Slack.ChatPostEphemeralResponse chatPostEphemeral(Slack.ChatPostEphemeralRequest req) { }
	global Slack.ChatPostMessageResponse chatPostMessage(Slack.ChatPostMessageRequest req) { }
	global Slack.ChatScheduleMessageResponse chatScheduleMessage(Slack.ChatScheduleMessageRequest req) { }
	global Slack.ChatScheduledMessagesListResponse chatScheduledMessagesList(Slack.ChatScheduledMessagesListRequest req) { }
	global Slack.ChatUpdateResponse chatUpdate(Slack.ChatUpdateRequest req) { }
	global Object clone() { }
	global Slack.ConversationsArchiveResponse conversationsArchive(Slack.ConversationsArchiveRequest req) { }
	global Slack.ConversationsCloseResponse conversationsClose(Slack.ConversationsCloseRequest req) { }
	global Slack.ConversationsCreateResponse conversationsCreate(Slack.ConversationsCreateRequest req) { }
	global Slack.ConversationsHistoryResponse conversationsHistory(Slack.ConversationsHistoryRequest req) { }
	global Slack.ConversationsInfoResponse conversationsInfo(Slack.ConversationsInfoRequest req) { }
	global Slack.ConversationsInviteResponse conversationsInvite(Slack.ConversationsInviteRequest req) { }
	global Slack.ConversationsJoinResponse conversationsJoin(Slack.ConversationsJoinRequest req) { }
	global Slack.ConversationsKickResponse conversationsKick(Slack.ConversationsKickRequest req) { }
	global Slack.ConversationsLeaveResponse conversationsLeave(Slack.ConversationsLeaveRequest req) { }
	global Slack.ConversationsListResponse conversationsList(Slack.ConversationsListRequest req) { }
	global Slack.ConversationsMarkResponse conversationsMark(Slack.ConversationsMarkRequest req) { }
	global Slack.ConversationsMembersResponse conversationsMembers(Slack.ConversationsMembersRequest req) { }
	global Slack.ConversationsOpenResponse conversationsOpen(Slack.ConversationsOpenRequest req) { }
	global Slack.ConversationsRenameResponse conversationsRename(Slack.ConversationsRenameRequest req) { }
	global Slack.ConversationsRepliesResponse conversationsReplies(Slack.ConversationsRepliesRequest req) { }
	global Slack.ConversationsSetPurposeResponse conversationsSetPurpose(Slack.ConversationsSetPurposeRequest req) { }
	global Slack.ConversationsSetTopicResponse conversationsSetTopic(Slack.ConversationsSetTopicRequest req) { }
	global Slack.ConversationsUnarchiveResponse conversationsUnarchive(Slack.ConversationsUnarchiveRequest req) { }
	global Slack.DndEndDndResponse dndEndDnd(Slack.DndEndDndRequest req) { }
	global Slack.DndEndSnoozeResponse dndEndSnooze(Slack.DndEndSnoozeRequest req) { }
	global Slack.DndInfoResponse dndInfo(Slack.DndInfoRequest req) { }
	global Slack.DndSetSnoozeResponse dndSetSnooze(Slack.DndSetSnoozeRequest req) { }
	global Slack.DndTeamInfoResponse dndTeamInfo(Slack.DndTeamInfoRequest req) { }
	global Slack.EmojiListResponse emojiList(Slack.EmojiListRequest req) { }
	global Slack.FilesDeleteResponse filesDelete(Slack.FilesDeleteRequest req) { }
	global Slack.FilesInfoResponse filesInfo(Slack.FilesInfoRequest req) { }
	global Slack.FilesListResponse filesList(Slack.FilesListRequest req) { }
	global Slack.FilesRemoteInfoResponse filesRemoteInfo(Slack.FilesRemoteInfoRequest req) { }
	global Slack.FilesRemoteListResponse filesRemoteList(Slack.FilesRemoteListRequest req) { }
	global Slack.FilesRemoteShareResponse filesRemoteShare(Slack.FilesRemoteShareRequest req) { }
	global Slack.FilesRevokePublicURLResponse filesRevokePublicURL(Slack.FilesRevokePublicURLRequest req) { }
	global Slack.FilesSharedPublicURLResponse filesSharedPublicURL(Slack.FilesSharedPublicURLRequest req) { }
	global Slack.MigrationExchangeResponse migrationExchange(Slack.MigrationExchangeRequest req) { }
	global Slack.PinsAddResponse pinsAdd(Slack.PinsAddRequest req) { }
	global Slack.PinsListResponse pinsList(Slack.PinsListRequest req) { }
	global Slack.PinsRemoveResponse pinsRemove(Slack.PinsRemoveRequest req) { }
	global Slack.ReactionsAddResponse reactionsAdd(Slack.ReactionsAddRequest req) { }
	global Slack.ReactionsGetResponse reactionsGet(Slack.ReactionsGetRequest req) { }
	global Slack.ReactionsListResponse reactionsList(Slack.ReactionsListRequest req) { }
	global Slack.ReactionsRemoveResponse reactionsRemove(Slack.ReactionsRemoveRequest req) { }
	global Slack.RemindersAddResponse remindersAdd(Slack.RemindersAddRequest req) { }
	global Slack.RemindersCompleteResponse remindersComplete(Slack.RemindersCompleteRequest req) { }
	global Slack.RemindersDeleteResponse remindersDelete(Slack.RemindersDeleteRequest req) { }
	global Slack.RemindersInfoResponse remindersInfo(Slack.RemindersInfoRequest req) { }
	global Slack.RemindersListResponse remindersList(Slack.RemindersListRequest req) { }
	global Slack.SearchAllResponse searchAll(Slack.SearchAllRequest req) { }
	global Slack.SearchFilesResponse searchFiles(Slack.SearchFilesRequest req) { }
	global Slack.SearchMessagesResponse searchMessages(Slack.SearchMessagesRequest req) { }
	global Slack.StarsAddResponse starsAdd(Slack.StarsAddRequest req) { }
	global Slack.StarsListResponse starsList(Slack.StarsListRequest req) { }
	global Slack.StarsRemoveResponse starsRemove(Slack.StarsRemoveRequest req) { }
	global Slack.TeamAccessLogsResponse teamAccessLogs(Slack.TeamAccessLogsRequest req) { }
	global Slack.TeamBillableInfoResponse teamBillableInfo(Slack.TeamBillableInfoRequest req) { }
	global Slack.TeamInfoResponse teamInfo(Slack.TeamInfoRequest req) { }
	global Slack.TeamIntegrationLogsResponse teamIntegrationLogs(Slack.TeamIntegrationLogsRequest req) { }
	global Slack.TeamProfileGetResponse teamProfileGet(Slack.TeamProfileGetRequest req) { }
	global Slack.UsergroupsCreateResponse usergroupsCreate(Slack.UsergroupsCreateRequest req) { }
	global Slack.UsergroupsDisableResponse usergroupsDisable(Slack.UsergroupsDisableRequest req) { }
	global Slack.UsergroupsEnableResponse usergroupsEnable(Slack.UsergroupsEnableRequest req) { }
	global Slack.UsergroupsListResponse usergroupsList(Slack.UsergroupsListRequest req) { }
	global Slack.UsergroupsUpdateResponse usergroupsUpdate(Slack.UsergroupsUpdateRequest req) { }
	global Slack.UsergroupsUsersListResponse usergroupsUsersList(Slack.UsergroupsUsersListRequest req) { }
	global Slack.UsergroupsUsersUpdateResponse usergroupsUsersUpdate(Slack.UsergroupsUsersUpdateRequest req) { }
	global Slack.UsersConversationsResponse usersConversations(Slack.UsersConversationsRequest req) { }
	global Slack.UsersDeletePhotoResponse usersDeletePhoto(Slack.UsersDeletePhotoRequest req) { }
	global Slack.UsersGetPresenceResponse usersGetPresence(Slack.UsersGetPresenceRequest req) { }
	global Slack.UsersIdentityResponse usersIdentity(Slack.UsersIdentityRequest req) { }
	global Slack.UsersInfoResponse usersInfo(Slack.UsersInfoRequest req) { }
	global Slack.UsersListResponse usersList(Slack.UsersListRequest req) { }
	global Slack.UsersLookupByEmailResponse usersLookupByEmail(Slack.UsersLookupByEmailRequest req) { }
	global Slack.UsersProfileGetResponse usersProfileGet(Slack.UsersProfileGetRequest req) { }
	global Slack.UsersProfileSetResponse usersProfileSet(Slack.UsersProfileSetRequest req) { }
	global Slack.UsersSetActiveResponse usersSetActive(Slack.UsersSetActiveRequest req) { }
	global Slack.UsersSetPresenceResponse usersSetPresence(Slack.UsersSetPresenceRequest req) { }
	global Slack.ViewsOpenResponse viewsOpen(Slack.ViewsOpenRequest req) { }
	global Slack.ViewsPushResponse viewsPush(Slack.ViewsPushRequest req) { }
	global Slack.ViewsUpdateResponse viewsUpdate(Slack.ViewsUpdateRequest req) { }

}