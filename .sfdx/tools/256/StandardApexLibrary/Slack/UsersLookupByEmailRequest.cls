global class UsersLookupByEmailRequest {
	global static Slack.UsersLookupByEmailRequest.Builder builder() { }
	global Object clone() { }
	global String getEmail() { }
	global String toString() { }
global class Builder {
	global UsersLookupByEmailRequest.Builder() { }
	global Slack.UsersLookupByEmailRequest build() { }
	global Object clone() { }
	global Slack.UsersLookupByEmailRequest.Builder email(String email) { }

}

}