global class UsersSetPresenceRequest {
	global static Slack.UsersSetPresenceRequest.Builder builder() { }
	global Object clone() { }
	global String getPresence() { }
	global String toString() { }
global class Builder {
	global UsersSetPresenceRequest.Builder() { }
	global Slack.UsersSetPresenceRequest build() { }
	global Object clone() { }
	global Slack.UsersSetPresenceRequest.Builder presence(String presence) { }

}

}