global class UsersGetPresenceResponse {
	global UsersGetPresenceResponse() { }
	global Object clone() { }
	global Integer getConnectionCount() { }
	global String getError() { }
	global Map<String,List<String>> getHttpResponseHeaders() { }
	global Integer getLastActivity() { }
	global String getNeeded() { }
	global String getPresence() { }
	global String getProvided() { }
	global String getWarning() { }
	global Boolean isAutoAway() { }
	global Boolean isManualAway() { }
	global Boolean isOk() { }
	global Boolean isOnline() { }
	global void setAutoAway(Boolean autoAway) { }
	global void setConnectionCount(Integer connectionCount) { }
	global void setError(String error) { }
	global void setHttpResponseHeaders(Map<String,List<String>> httpResponseHeaders) { }
	global void setLastActivity(Integer lastActivity) { }
	global void setManualAway(Boolean manualAway) { }
	global void setNeeded(String needed) { }
	global void setOk(Boolean ok) { }
	global void setOnline(Boolean online) { }
	global void setPresence(String presence) { }
	global void setProvided(String provided) { }
	global void setWarning(String warning) { }
	global String toString() { }

}