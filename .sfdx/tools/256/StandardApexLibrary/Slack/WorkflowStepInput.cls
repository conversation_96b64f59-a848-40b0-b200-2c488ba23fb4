global class WorkflowStepInput {
	global WorkflowStepInput() { }
	global Object clone() { }
	global Boolean getSkipVariableReplacement() { }
	global Object getValue() { }
	global Map<String,Object> getVariables() { }
	global void setSkipVariableReplacement(Boolean skipVariableReplacement) { }
	global void setValue(Object value) { }
	global void setVariables(Map<String,Object> variables) { }
	global String toString() { }

}