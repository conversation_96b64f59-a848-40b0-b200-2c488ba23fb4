global class UserResourceDeniedEvent {
	global UserResourceDeniedEvent() { }
	global Object clone() { }
	global List<String> getScopes() { }
	global String getSubtype() { }
	global String getTriggerId() { }
	global String getType() { }
	global String getUser() { }
	global void setScopes(List<String> scopes) { }
	global void setTriggerId(String triggerId) { }
	global void setUser(String user) { }
	global String toString() { }

}