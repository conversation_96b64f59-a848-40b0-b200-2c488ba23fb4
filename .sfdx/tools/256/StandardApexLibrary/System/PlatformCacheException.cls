global class PlatformCacheException extends Exception {
	global PlatformCacheException(String param0, Exception param1) { }
	global PlatformCacheException(Exception param0) { }
	global PlatformCacheException(String param0) { }
	global PlatformCacheException() { }
	global Boolean equals(Object obj) { }
	global Exception getCause() { }
	global Map<String,Set<String>> getInaccessibleFields() { }
	global Integer getLineNumber() { }
	global String getMessage() { }
	global String getStackTraceString() { }
	global String getTypeName() { }
	global Integer hashCode() { }
	global void initCause(Exception cause) { }
	global void setMessage(String message) { }
	global String toString() { }

}