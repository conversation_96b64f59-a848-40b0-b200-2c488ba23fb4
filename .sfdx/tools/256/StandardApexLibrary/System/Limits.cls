global class Limits {
	global static Integer getAggregateQueries() { }
	global static Integer getApexCursorRows() { }
	global static Integer getAsyncCalls() { }
	global static Integer getCallouts() { }
	global static Integer getChildRelationshipsDescribes() { }
	global static Integer getCpuTime() { }
	global static Integer getDatabaseTime() { }
	global static Integer getDmlRows() { }
	global static Integer getDmlStatements() { }
	global static Integer getEmailInvocations() { }
	global static Integer getFetchCallsOnApexCursor() { }
	global static Integer getFieldSetsDescribes() { }
	global static Integer getFieldsDescribes() { }
	global static Integer getFindSimilarCalls() { }
	global static Integer getFutureCalls() { }
	global static Integer getHeapSize() { }
	global static Integer getLimitAggregateQueries() { }
	global static Integer getLimitApexCursorRows() { }
	global static Integer getLimitAsyncCalls() { }
	global static Integer getLimitCallouts() { }
	global static Integer getLimitChildRelationshipsDescribes() { }
	global static Integer getLimitCpuTime() { }
	global static Integer getLimitDatabaseTime() { }
	global static Integer getLimitDmlRows() { }
	global static Integer getLimitDmlStatements() { }
	global static Integer getLimitEmailInvocations() { }
	global static Integer getLimitFetchCallsOnApexCursor() { }
	global static Integer getLimitFieldSetsDescribes() { }
	global static Integer getLimitFieldsDescribes() { }
	global static Integer getLimitFindSimilarCalls() { }
	global static Integer getLimitFutureCalls() { }
	global static Integer getLimitHeapSize() { }
	global static Integer getLimitMobilePushApexCalls() { }
	global static Integer getLimitPicklistDescribes() { }
	global static Integer getLimitPublishImmediateDML() { }
	global static Integer getLimitQueries() { }
	global static Integer getLimitQueryLocatorRows() { }
	global static Integer getLimitQueryRows() { }
	global static Integer getLimitQueueableJobs() { }
	global static Integer getLimitRecordTypesDescribes() { }
	global static Integer getLimitRunAs() { }
	global static Integer getLimitSavepointRollbacks() { }
	global static Integer getLimitSavepoints() { }
	global static Integer getLimitScriptStatements() { }
	global static Integer getLimitSoslQueries() { }
	global static Integer getMobilePushApexCalls() { }
	global static Integer getPicklistDescribes() { }
	global static Integer getPublishImmediateDML() { }
	global static Integer getQueries() { }
	global static Integer getQueryLocatorRows() { }
	global static Integer getQueryRows() { }
	global static Integer getQueueableJobs() { }
	global static Integer getRecordTypesDescribes() { }
	global static Integer getRunAs() { }
	global static Integer getSavepointRollbacks() { }
	global static Integer getSavepoints() { }
	global static Integer getScriptStatements() { }
	global static Integer getSoslQueries() { }

}