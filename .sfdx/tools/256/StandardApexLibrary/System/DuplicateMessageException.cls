global class DuplicateMessageException extends Exception {
	global DuplicateMessageException(String param0, Exception param1) { }
	global DuplicateMessageException(Exception param0) { }
	global DuplicateMessageException(String param0) { }
	global DuplicateMessageException() { }
	global Boolean equals(Object obj) { }
	global Exception getCause() { }
	global Map<String,Set<String>> getInaccessibleFields() { }
	global Integer getLineNumber() { }
	global String getMessage() { }
	global String getStackTraceString() { }
	global String getTypeName() { }
	global Integer hashCode() { }
	global void initCause(Exception cause) { }
	global void setMessage(String message) { }
	global String toString() { }

}