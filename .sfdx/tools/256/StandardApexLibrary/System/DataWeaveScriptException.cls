global class DataWeaveScriptException extends Exception {
	global DataWeaveScriptException(String param0, Exception param1) { }
	global DataWeaveScriptException(Exception param0) { }
	global DataWeaveScriptException(String param0) { }
	global DataWeaveScriptException() { }
	global Boolean equals(Object obj) { }
	global Exception getCause() { }
	global Map<String,Set<String>> getInaccessibleFields() { }
	global Integer getLineNumber() { }
	global String getMessage() { }
	global String getStackTraceString() { }
	global String getTypeName() { }
	global Integer hashCode() { }
	global void initCause(Exception cause) { }
	global void setMessage(String message) { }
	global String toString() { }

}