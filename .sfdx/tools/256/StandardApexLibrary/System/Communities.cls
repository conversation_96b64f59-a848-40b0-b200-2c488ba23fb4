global class Communities {
	global Communities() { }
	global Object clone() { }
	global static System.PageReference communitiesLanding(String startUrl) { }
	global static System.PageReference communitiesLanding() { }
	global static System.PageReference forwardToAuthPage(String startUrl) { }
	global static String getCSS() { }
	global static System.PageReference internalLogin(String startUrl) { }
	global static System.PageReference login(String username, String password, String startUrl) { }

}