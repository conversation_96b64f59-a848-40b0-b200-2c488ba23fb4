global class DomainCreator {
	global DomainCreator() { }
	global Object clone() { }
	global static String getContentHostname() { }
	global static String getExperienceCloudSitesBuilderHostname() { }
	global static String getExperienceCloudSitesHostname() { }
	global static String getExperienceCloudSitesLivePreviewHostname() { }
	global static String getExperienceCloudSitesPreviewHostname() { }
	global static String getLightningContainerComponentHostname(String packageName) { }
	global static String getLightningHostname() { }
	global static String getOrgMyDomainHostname() { }
	global static String getSalesforceSitesHostname() { }
	global static String getSetupHostname() { }
	global static String getVisualforceHostname(String packageName) { }

}