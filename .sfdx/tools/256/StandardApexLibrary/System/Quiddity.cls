global enum Quiddity {
ANONYMOUS,
AURA,
<PERSON><PERSON>H_ACS,
BATCH_APEX,
BATCH_CHUN<PERSON>_PARALLEL,
BATCH_CHUNK_SERIAL,
B<PERSON><PERSON>K_API,
COMMERCE_INTEGRATION,
DISCOVERABLE_LOGIN,
<PERSON><PERSON><PERSON><PERSON><PERSON>_SERVICE_CALL<PERSON>CK,
<PERSON>UN<PERSON><PERSON>_CALLBACK,
<PERSON><PERSON>UR<PERSON>,
INBOUND_EMAIL_SERVICE,
INVOCA<PERSON>E_ACTION,
IOT,
PLATFORM_EVENT_PUBLISH_CALLBACK,
POST_INSTALL_SCRIPT,
QUEUEABLE,
QUICK_ACTION,
REMOTE_ACTION,
REST,
RUNTEST_ASYNC,
RUNTEST_DEPLOY,
RUNTE<PERSON>_SYNC,
<PERSON>H<PERSON><PERSON>LE<PERSON>,
<PERSON>OA<PERSON>,
<PERSON><PERSON><PERSON><PERSON><PERSON>NO<PERSON>,
TRANSACTION_FIN<PERSON>IZER_QUEUEABLE,
TRANSACTION_SECURITY_POLICY,
UNDEFINED,
VF
}