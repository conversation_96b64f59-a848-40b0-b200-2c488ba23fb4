global class Math {
	global static Double E;
	global static Double PI;
	global Math() { }
	global static Decimal abs(Decimal x) { }
	global static Double abs(Double x) { }
	global static Integer abs(Integer x) { }
	global static Long abs(Long x) { }
	global static Decimal acos(Decimal x) { }
	global static Double acos(Double x) { }
	global static Decimal asin(Decimal x) { }
	global static Double asin(Double x) { }
	global static Decimal atan(Decimal x) { }
	global static Double atan(Double x) { }
	global static Decimal atan2(Decimal x, Decimal y) { }
	global static Double atan2(Double x, Double y) { }
	global static Decimal cbrt(Decimal x) { }
	global static Double cbrt(Double x) { }
	global static Decimal ceil(Decimal x) { }
	global static Double ceil(Double x) { }
	global Object clone() { }
	global static Decimal cos(Decimal x) { }
	global static Double cos(Double x) { }
	global static Decimal cosh(Decimal x) { }
	global static Double cosh(Double x) { }
	global static Decimal exp(Decimal x) { }
	global static Double exp(Double x) { }
	global static Decimal floor(Decimal x) { }
	global static Double floor(Double x) { }
	global static Decimal log(Decimal x) { }
	global static Double log(Double x) { }
	global static Decimal log10(Decimal x) { }
	global static Double log10(Double x) { }
	global static Decimal max(Decimal x, Decimal y) { }
	global static Double max(Double x, Double y) { }
	global static Integer max(Integer x, Integer y) { }
	global static Long max(Long x, Long y) { }
	global static Decimal min(Decimal x, Decimal y) { }
	global static Double min(Double x, Double y) { }
	global static Integer min(Integer x, Integer y) { }
	global static Long min(Long x, Long y) { }
	global static Integer mod(Integer x, Integer y) { }
	global static Long mod(Long x, Long y) { }
	global static Double pow(Double base, Double exp) { }
	global static Double random() { }
	global static Decimal rint(Decimal x) { }
	global static Double rint(Double x) { }
	global static Integer round(Decimal x) { }
	global static Integer round(Double x) { }
	global static Long roundToLong(Decimal x) { }
	global static Long roundToLong(Double x) { }
	global static Decimal signum(Decimal x) { }
	global static Double signum(Double x) { }
	global static Decimal sin(Decimal x) { }
	global static Double sin(Double x) { }
	global static Decimal sinh(Decimal x) { }
	global static Double sinh(Double x) { }
	global static Decimal sqrt(Decimal x) { }
	global static Double sqrt(Double x) { }
	global static Decimal tan(Decimal x) { }
	global static Double tan(Double x) { }
	global static Decimal tanh(Decimal x) { }
	global static Double tanh(Double x) { }

}